<% var item, key %>

<% htmlWebpackPlugin.options.appMountIds = htmlWebpackPlugin.options.appMountIds || [] %>
<% htmlWebpackPlugin.options.lang = htmlWebpackPlugin.options.lang || "en" %>
<% htmlWebpackPlugin.options.links = htmlWebpackPlugin.options.links || [] %>
<% htmlWebpackPlugin.options.meta = htmlWebpackPlugin.options.meta || [] %>
<% htmlWebpackPlugin.options.scripts = htmlWebpackPlugin.options.scripts || [] %>

<!DOCTYPE html>
<html lang="<%= htmlWebpackPlugin.options.lang %>" <% if (htmlWebpackPlugin.files.manifest) { %> manifest="<%= htmlWebpackPlugin.files.manifest %>"<% } %>>
<head>
  <meta charset="utf-8">
  <meta content="ie=edge" http-equiv="x-ua-compatible">

  <% if (htmlWebpackPlugin.options.baseHref) { %>
  <base href="<%= htmlWebpackPlugin.options.baseHref %>">
  <% } %>

  <% if (Array.isArray(htmlWebpackPlugin.options.meta)) { %>
  <% for (item of htmlWebpackPlugin.options.meta) { %>
  <meta<% for (key in item) { %> <%= key %>="<%= item[key] %>"<% } %>>
  <% } %>
  <% } %>

  <title><%= htmlWebpackPlugin.options.title %></title>

  <% if (htmlWebpackPlugin.files.favicon) { %>
  <link href="<%= htmlWebpackPlugin.files.favicon %>" rel="shortcut icon">
  <% } %>

  <% if (htmlWebpackPlugin.options.mobile) { %>
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <% } %>

  <% for (item of htmlWebpackPlugin.options.links) { %>
  <% if (typeof item === 'string' || item instanceof String) { item = { href: item, rel: 'stylesheet' } } %>
  <link<% for (key in item) { %> <%= key %>="<%= item[key] %>"<% } %>>
  <% } %>

  <% for (item of htmlWebpackPlugin.options.scripts) { %>
    <% if (typeof item === 'string' || item instanceof String) { item = { src: item, type: 'text/javascript' } } %>
    <script<% for (key in item) { %> <%= key %>="<%= item[key] %>"<% } %> crossorigin></script>
  <% } %>
  <style type="text/css">
    font-family: ff-clan-web-pro, 'Helvetica Neue', Helvetica, sans-serif;
    font-weight: 400;
    font-size: 0.875em;
    line-height: 1.71429;

    *,
    *:before,
    *:after {
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
    }
    body {
      margin: 0; padding: 0;
    }
  </style>
</head>
<body>
<% if (htmlWebpackPlugin.options.unsupportedBrowser) { %>
<style> .unsupported-browser { display: none; }</style>
<div class="unsupported-browser">
  Sorry, your browser is not supported. Please upgrade to the latest version or switch your browser to use this
  site. See <a href="http://outdatedbrowser.com/">outdatedbrowser.com</a> for options.
</div>
<% } %>

<% if (htmlWebpackPlugin.options.appMountId) { %>
<div id="<%= htmlWebpackPlugin.options.appMountId %>"></div>
<% } %>
<% for (item of htmlWebpackPlugin.options.appMountIds) { %>
<div id="<%= item %>"></div>
<% } %>

<% if (htmlWebpackPlugin.options.window) { %>
<script type="text/javascript">
  <% for (key in htmlWebpackPlugin.options.window) { %>
  window['<%= key %>'] = <%= JSON.stringify(htmlWebpackPlugin.options.window[key]) %>;
  <% } %>
</script>
<% } %>

<% if (htmlWebpackPlugin.options.inlineManifestWebpackName) { %>
<%= htmlWebpackPlugin.files[htmlWebpackPlugin.options.inlineManifestWebpackName] %>
<% } %>

<% if (htmlWebpackPlugin.options.devServer) { %>
<script src="<%= htmlWebpackPlugin.options.devServer %>/webpack-dev-server.js" type="text/javascript"></script>
<% } %>

<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-64694404-19', {
    'storage': 'none',
    'clientId': localStorage.getItem('ga:clientId')
  });
  ga(function(tracker) {
      localStorage.setItem('ga:clientId', tracker.get('clientId'));
  });
  ga('set', 'checkProtocolTask', null); // Disable file protocol checking.
  ga('set', 'checkStorageTask', null); // Disable cookie storage checking.
  ga('set', 'historyImportTask', null); // Disable history checking (requires reading from cookies).
  ga('set', 'page', 'keplergl-jupyter-html');

  ga('send', 'pageview');
</script>
</body>
</html>
