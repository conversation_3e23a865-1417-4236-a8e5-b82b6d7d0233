# User guides

Kepler.gl is designed for geospatial data analysis. It allows technical and non-technical audiences to visualize trends in a city or region. With Kepler.gl, you can…

Visualize a large amount of location data in your browser.
Playback geo-temporal trends over time.
Explore, filter, and deeply engage with location data to gain insight

See the sample maps in the demo app for more examples.

![Kepler.gl sample map](https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/documentation/image11.png "Kepler.gl sample map")

This guide will teach you how to perform data analysis in Kepler.gl by adding data to a map, creating layers, adding filters, and more.

## Table of contents:

#### [Get Started](./j-get-started.md)

#### [The kepler.gl workflow](./b-kepler-gl-workflow/README.md)
* [Add data to the map](./b-kepler-gl-workflow/a-add-data-to-the-map.md)
* [Adding data layers](./b-kepler-gl-workflow/b-add-data-layers/a-adding-data-layers.md)
* [Create a layer](./b-kepler-gl-workflow/b-add-data-layers/b-create-a-layer.md)
* [Hide, edit and delete layers](./b-kepler-gl-workflow/b-add-data-layers/c-hide-edit-and-delete-layers.md)
* [Blend and rearrange layers](./b-kepler-gl-workflow/b-add-data-layers/d-blend-and-rearrange-layers.md)

#### [Layers](./c-types-of-layers/README.md)

* [Point](./c-types-of-layers/a-point.md)
* [Arc](./c-types-of-layers/b-arc.md)
* [Line](./c-types-of-layers/c-line.md)
* [Grid](./c-types-of-layers/d-grid.md)
* [Polygon](./c-types-of-layers/e-polygon.md)
* [Cluster](./c-types-of-layers/f-cluster.md)
* [Icon](./c-types-of-layers/g-icon.md)
* [Hexbin](./c-types-of-layers/h-hexbin.md)
* [Heatmap](./c-types-of-layers/i-heatmap.md)
* [H3](./c-types-of-layers/j-h3.md)
* [Trip](./c-types-of-layers/k-trip.md)
* [S2](./c-types-of-layers/l-s2.md)
* [Vector](./c-types-of-layers/vector.md)

#### [Layer attributes](./d-layer-attributes.md)

#### [Color Palettes](./l-color-attributes.md)

#### [Filters](./e-filters.md)

#### [Map styles](./f-map-styles.md#map-styles.md)
* [Base map styles](./f-map-styles.md#base-map-styles.md)
* [Map layers](./f-map-styles.md#map-layers.md)
* [Custom styles](./f-map-styles.md#custom-styles.md)

#### [Interactions](./g-interactions.md)
* [Tooltips](./g-interactions.md#tooltips)
* [Brushing](./g-interactions.md#brushing)
* [Display Coordinates](./g-interactions.md#display-coordinates)

#### [Map Settings](./m-map-settings.md)
* [View maps in 3d](./m-map-settings.md#view-maps-in-3d)
* [Display legend](./m-map-settings.md#display-legend)
* [Split maps](./m-map-settings.md#split-maps)

#### [SQL Data Explorer](./sql-data-explorer.md)

#### [AI Assistant](./ai-assistant.md)

#### [Time playback](./h-playback.md)

#### [Save and export](./k-save-and-export.md)

#### [FAQ](./i-FAQ.md)
