# Dockerfile para Kepler.gl + Turfeiras V3
# Estágio de build para aplicação Kepler.gl com módulos Turfeiras

# Estágio de build
FROM node:18-alpine as builder

WORKDIR /app

# Instalar dependências do sistema necessárias para compilação
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    pkgconfig \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    mesa-dev \
    libxi-dev \
    libxext-dev \
    libx11-dev \
    xvfb \
    && rm -rf /var/cache/apk/*

# Habilitar Corepack e configurar Yarn
RUN corepack enable && corepack prepare yarn@4.4.0 --activate

# Copiar package.json e yarn.lock
COPY package.json yarn.lock ./
COPY .yarnrc.yml ./

# Definir variáveis de ambiente para build headless
ENV DISPLAY=:99
ENV NODE_ENV=production
ENV HEADLESS_GL_FORCE_SOFTWARE=1
ENV LIBGL_ALWAYS_SOFTWARE=1
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# Instalar dependências do workspace principal
RUN yarn install --network-timeout 300000

# Copiar código fonte
COPY . .

# Instalar dependências do demo-app especificamente
WORKDIR /app/examples/demo-app
RUN yarn install --network-timeout 300000

# Executar build da aplicação com esbuild para Docker
RUN yarn build:docker

# Verificar se o build foi criado
RUN ls -la dist/

# Estágio de produção - Servir com nginx
FROM nginx:alpine

# Copiar build da aplicação
COPY --from=builder /app/examples/demo-app/dist /usr/share/nginx/html

# Copiar configuração do nginx
COPY docker/default.conf /etc/nginx/conf.d/default.conf

# Criar diretórios necessários
RUN mkdir -p /usr/share/nginx/html/data
RUN mkdir -p /usr/share/nginx/html/uploads

# Expor porta
EXPOSE 80

# Comando para iniciar nginx
CMD ["nginx", "-g", "daemon off;"]
