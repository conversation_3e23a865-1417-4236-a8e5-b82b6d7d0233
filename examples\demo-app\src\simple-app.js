// SPDX-License-Identifier: MIT
// Versão SUPER SIMPLES do Kepler.gl sem customizações

import React from 'react';
import {createRoot} from 'react-dom/client';
import {Provider} from 'react-redux';
import {enhanceReduxMiddleware} from '@kepler.gl/reducers';
import KeplerGl from '@kepler.gl/components';
import {createStore, combineReducers, applyMiddleware} from 'redux';

// Configuração mínima do Redux
const reducers = combineReducers({
  keplerGl: KeplerGl.reducer
});

const store = createStore(
  reducers,
  {},
  applyMiddleware(...enhanceReduxMiddleware([]))
);

// Componente da aplicação super simples
const App = () => (
  <Provider store={store}>
    <KeplerGl
      id="map"
      width={window.innerWidth}
      height={window.innerHeight}
      appName="TurfeirasBrasil"
      version="v1.0"
    />
  </Provider>
);

// Renderizar
const container = document.getElementById('root');
const root = createRoot(container);
root.render(<App />);

console.log('🌿 TurfeirasBrasil - App Simples Iniciado!');
