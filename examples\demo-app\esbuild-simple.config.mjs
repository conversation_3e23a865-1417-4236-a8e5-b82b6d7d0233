import esbuild from 'esbuild';
import { spawn } from 'child_process';
import { join } from 'path';
import fs from 'fs';

// Configuração simplificada para desenvolvimento
const config = {
  entryPoints: ['src/main.js'],
  bundle: true,
  outfile: 'dist/bundle.js',
  format: 'iife',
  platform: 'browser',
  target: 'es2020',
  sourcemap: true,
  minify: false,
  define: {
    'process.env.NODE_ENV': '"development"',
    'process.env.MapboxAccessToken': '"pk.eyJ1IjoidWJlcmRhdGEiLCJhIjoiY2pudzRtaWloMDAzcTN2bzN1aXdxZHB5bSJ9.2bkfz2lBzd5fcCWEo-XuTA"',
    'process.env.DropboxClientId': '""',
    'process.env.MapboxExportToken': '""',
    'process.env.CartoClientId': '""',
    'process.env.FoursquareClientId': '""',
    'process.env.FoursquareDomain': '""',
    'process.env.FoursquareAPIURL': '""',
    'process.env.FoursquareUserMapsURL': '""',
    'global': 'globalThis'
  },
  loader: {
    '.js': 'jsx',
    '.jsx': 'jsx',
    '.ts': 'tsx',
    '.tsx': 'tsx',
    '.png': 'file',
    '.jpg': 'file',
    '.jpeg': 'file',
    '.gif': 'file',
    '.svg': 'file',
    '.woff': 'file',
    '.woff2': 'file',
    '.ttf': 'file',
    '.eot': 'file'
  },
  external: [],
  plugins: [],
  resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
  jsx: 'automatic'
};

// Função para iniciar servidor de desenvolvimento
function startDevServer(port = 8080, host = '0.0.0.0') {
  const ctx = esbuild.context(config);

  ctx.then(context => {
    context.serve({
      servedir: '.',
      port: port,
      host: host
    }).then(server => {
      console.log(`🌿 Turfeiras Brasil - Servidor de desenvolvimento iniciado`);
      console.log(`📍 Local: http://localhost:${port}`);
      console.log(`🌐 Network: http://${host}:${port}`);
      console.log(`🔄 Hot reload ativo`);

      // Não tentar abrir browser automaticamente no Docker
      if (!process.env.DOCKER_CONTAINER) {
        console.log(`🚀 Abrindo browser...`);
      }
    }).catch(err => {
      console.error('❌ Erro ao iniciar servidor:', err);
      process.exit(1);
    });
  }).catch(err => {
    console.error('❌ Erro ao criar contexto esbuild:', err);
    process.exit(1);
  });
}

// Função para build de produção
function buildProduction() {
  const prodConfig = {
    ...config,
    minify: true,
    sourcemap: false,
    define: {
      ...config.define,
      'process.env.NODE_ENV': '"production"'
    }
  };

  esbuild.build(prodConfig).then(() => {
    console.log('✅ Build de produção concluído');
  }).catch(err => {
    console.error('❌ Erro no build:', err);
    process.exit(1);
  });
}

// Processar argumentos da linha de comando
const args = process.argv.slice(2);
const isStart = args.includes('--start');
const port = parseInt(args.find(arg => arg.startsWith('--port='))?.split('=')[1]) || 8080;
const host = args.find(arg => arg.startsWith('--host='))?.split('=')[1] || '0.0.0.0';

if (isStart) {
  startDevServer(port, host);
} else {
  buildProduction();
}