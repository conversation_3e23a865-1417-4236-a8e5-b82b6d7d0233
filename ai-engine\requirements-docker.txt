# Requirements simplificados para Docker - Turfeiras V3
# Versão mínima funcional

# Bibliotecas essenciais
numpy==1.24.3
pandas==2.0.1
scikit-learn==1.2.2

# Web framework
flask==2.3.2
flask-cors==3.0.10

# Servidor web
gunicorn==21.2.0

# Utilitários
requests==2.31.0
PyYAML==6.0
joblib==1.2.0

# Logging
loguru==0.7.0

# JSON e serialização
# pickle5 removido - não necessário para Python 3.10+

# Para análise estatística básica
scipy==1.10.1

# Visualização básica (opcional)
matplotlib==3.7.1

# Processamento de dados
openpyxl==3.1.2
