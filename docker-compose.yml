version: '3.8'
# Docker Compose para Sistema Turfeiras V3
# Configuração completa para análise geomorfológica integrada ao Kepler.gl

services:
  # Serviço de desenvolvimento Turfeiras
  turfeiras-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: turfeiras-dev-container
    environment:
      - DOCKER_CONTAINER=true
      - NODE_ENV=development
      - PORT=8080
      - HOST=0.0.0.0
      - MapboxAccessToken=pk.eyJ1IjoidWJlcmRhdGEiLCJhIjoiY2pudzRtaWloMDAzcTN2bzN1aXdxZHB5bSJ9.2bkfz2lBzd5fcCWEo-XuTA
    volumes:
      # Mapeia todo o projeto para dentro do container, exceto node_modules.
      # Isso permite hot-reloading quando você edita os arquivos locais.
      - .:/app
      - /app/node_modules
      # Mapeia os node_modules dos workspaces para evitar que o volume acima os sobrescreva.
      - /app/examples/demo-app/node_modules
      - /app/src/node_modules
    ports:
      # Mapeia a porta do container (8080) para a porta do seu computador (8081).
      # O script "start" do demo-app roda na porta 8080.
      - "8081:8080"
    # Mantém o container rodando em modo interativo para o servidor de desenvolvimento.
    stdin_open: true
    tty: true

# Rede personalizada
networks:
  turfeiras-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
