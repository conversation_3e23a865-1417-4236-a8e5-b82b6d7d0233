{"scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider node server.js", "start-local": "NODE_OPTIONS=--openssl-legacy-provider WEBPACK_ENV=es6 node server.js"}, "dependencies": {"@kepler.gl/components": "^3.0.0-alpha.1", "@kepler.gl/reducers": "^3.0.0-alpha.1", "express": "^4.17.1", "global": "^4.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-palm": "^3.3.6", "react-redux": "^8.0.5", "react-virtualized": "^9.21.0", "redux-actions": "^2.2.1", "styled-components": "6.1.8"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/plugin-transform-class-properties": "^7.12.1", "@babel/plugin-transform-export-namespace-from": "^7.12.1", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "@babel/plugin-transform-optional-chaining": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-transform-typescript": "^7.16.8", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.1", "@babel/preset-typescript": "^7.16.7", "babel-loader": "^8.0.0", "webpack": "^4.29.0", "webpack-cli": "^3.2.1", "webpack-dev-middleware": "^3.7.0", "webpack-dev-server": "^3.1.14", "webpack-hot-middleware": "^2.25.0"}}