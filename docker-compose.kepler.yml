version: '3.8'

services:
  kepler-turfeiras:
    build:
      context: .
      dockerfile: Dockerfile.kepler
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - MapboxAccessToken=pk.eyJ1IjoidWJlcmRhdGEiLCJhIjoiY2pudzRtaWloMDAzcTN2bzN1aXdxZHB5bSJ9.2bkfz2lBzd5fcCWEo-XuTA
      - FOURSQUARE_API_KEY=
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run start
    restart: unless-stopped
