// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

// Add english messages here, other languages will use these
// if translations not available for every message

export const messages = {
  en: {
    tooltip: {
      showAiAssistantPanel: 'Show AI Assistant panel',
      hideAiAssistantPanel: 'Hide AI Assistant panel'
    },
    aiAssistantManager: {
      title: 'AI Assistant',
      aiProvider: 'AI Provider',
      llmModel: {
        title: 'Select LLM Model'
      },
      apiKey: {
        title: 'API Key',
        placeholder: 'Enter your API Key'
      },
      baseUrl: {
        title: 'Ollama Base URL',
        placeholder: 'Enter OllamaBase URL'
      },
      temperature: {
        title: 'Temperature'
      },
      topP: {
        title: 'Top P'
      },
      startChat: "Let's Chat"
    }
  },
  fi: {
    tooltip: {
      showAiAssistantPanel: 'Näytä AI-ohjainpaneeli',
      hideAiAssistantPanel: 'Piilota AI-ohjainpaneeli'
    },
    aiAssistantManager: {
      title: 'AI-ohjain',
      aiProvider: 'AI-toimittaja',
      llmModel: {
        title: '<PERSON><PERSON><PERSON> LLM-malli'
      },
      apiKey: {
        title: 'API-avain',
        placeholder: 'Syötä API-avain'
      },
      baseUrl: {
        title: 'Ollama Base URL',
        placeholder: 'Syötä Ollama Base URL'
      },
      temperature: {
        title: 'Lämpötila'
      },
      topP: {
        title: 'Top P'
      },
      startChat: 'Aloita keskustelu'
    }
  },
  ca: {
    tooltip: {
      showAiAssistantPanel: "Mostrar panell d'IA",
      hideAiAssistantPanel: "Ocultar panell d'IA"
    },
    aiAssistantManager: {
      title: 'IA',
      aiProvider: "Proveïdor d'IA",
      llmModel: {
        title: 'Seleccionar model LLM'
      },
      apiKey: {
        title: 'Clau API',
        placeholder: 'Introdueix la teva clau API'
      },
      baseUrl: {
        title: 'URL OllamaBase',
        placeholder: 'Introdueix URL OllamaBase'
      },
      temperature: {
        title: 'Temperatura'
      },
      topP: {
        title: 'Top P'
      },
      startChat: 'Començar xat'
    }
  },
  es: {
    tooltip: {
      showAiAssistantPanel: 'Mostrar panel de IA',
      hideAiAssistantPanel: 'Ocultar panel de IA'
    },
    aiAssistantManager: {
      title: 'IA',
      aiProvider: 'Proveedor de IA',
      llmModel: {
        title: 'Seleccionar modelo LLM'
      },
      apiKey: {
        title: 'Clave API',
        placeholder: 'Introduce tu clave API'
      },
      baseUrl: {
        title: 'URL OllamaBase',
        placeholder: 'Introduce URL OllamaBase'
      },
      temperature: {
        title: 'Temperatura'
      },
      topP: {
        title: 'Top P'
      },
      startChat: 'Iniciar chat'
    }
  },
  cn: {
    tooltip: {
      showAiAssistantPanel: '显示 AI 助手面板',
      hideAiAssistantPanel: '隐藏 AI 助手面板'
    },
    aiAssistantManager: {
      title: 'AI 助手',
      aiProvider: 'AI 提供商',
      llmModel: {
        title: '选择 LLM 模型'
      },
      apiKey: {
        title: 'API 密钥',
        placeholder: '输入你的 API 密钥'
      },
      baseUrl: {
        title: 'OllamaBase 网址',
        placeholder: '输入 OllamaBase 网址'
      },
      temperature: {
        title: '温度'
      },
      topP: {
        title: 'Top P'
      },
      startChat: '开始聊天'
    }
  },
  jp: {
    tooltip: {
      showAiAssistantPanel: 'AI アシスタントパネルを表示',
      hideAiAssistantPanel: 'AI アシスタントパネルを非表示'
    },
    aiAssistantManager: {
      title: 'AI アシスタント',
      aiProvider: 'AI プロバイダー',
      llmModel: {
        title: 'LLM モデルを選択'
      },
      apiKey: {
        title: 'API キー',
        placeholder: 'API キーを入力'
      },
      baseUrl: {
        title: 'Ollama Base URL',
        placeholder: 'OllamaBase URL を入力'
      },
      temperature: {
        title: '温度'
      },
      topP: {
        title: 'Top P'
      },
      startChat: 'チャットを開始'
    }
  }
};
