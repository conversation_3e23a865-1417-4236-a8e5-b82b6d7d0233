# Kepler.gl with Vite

This example uses [Kepler.gl](https://kepler.gl/) with Vite as the build tool.

## Development

### Installation

```bash
# Install dependencies
yarn install
```

### Development Server

To start the development server with hot module replacement:

```bash
yarn dev
```

### Production Build

To create a production build and preview it:

```bash
# Create production build
yarn build

# Preview production build
yarn preview
```
