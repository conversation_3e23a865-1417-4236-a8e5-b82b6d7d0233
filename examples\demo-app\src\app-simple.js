// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React, {Component} from 'react';
import {connect} from 'react-redux';
import KeplerGl from '@kepler.gl/components';
import {addDataToMap} from '@kepler.gl/actions';
import {injectComponents} from '@kepler.gl/components';

// Dados de exemplo de turfeiras brasileiras
const sampleTurfeirasData = {
  fields: [
    {name: 'id', type: 'integer'},
    {name: 'nome', type: 'string'},
    {name: 'latitude', type: 'real'},
    {name: 'longitude', type: 'real'},
    {name: 'area_ha', type: 'real'},
    {name: 'tipo', type: 'string'},
    {name: 'estado', type: 'string'}
  ],
  rows: [
    [1, 'Turfeira do Pantanal', -16.5, -56.6, 1200, 'Tropical', 'MT'],
    [2, 'Turfeira da Chapada', -14.2, -47.8, 850, '<PERSON><PERSON><PERSON>', '<PERSON>'],
    [3, 'Turfeira Amazônica', -3.1, -60.0, 2500, 'Tropical', 'AM'],
    [4, 'Turfeira do Planalto', -15.8, -48.1, 650, 'Cerrado', 'DF'],
    [5, 'Turfeira Costeira', -23.5, -46.6, 300, 'Atlântica', 'SP']
  ]
};

// Componente personalizado para o painel de turfeiras
const TurfeirasPanel = () => (
  <div style={{padding: '20px', color: '#fff'}}>
    <h3>🌿 Turfeiras Tropicais Brasil</h3>
    <p>Dados de turfeiras brasileiras:</p>
    <ul>
      <li>Total: 5 turfeiras mapeadas</li>
      <li>Área total: 5.500 hectares</li>
      <li>Estados: MT, GO, AM, DF, SP</li>
    </ul>
    <div style={{marginTop: '20px'}}>
      <strong>Tipos de turfeiras:</strong>
      <ul>
        <li>Tropical: 2 unidades</li>
        <li>Cerrado: 2 unidades</li>
        <li>Atlântica: 1 unidade</li>
      </ul>
    </div>
  </div>
);

// Configuração do Kepler.gl com componentes personalizados
const KeplerGlWithTurfeiras = injectComponents([
  // Mantém os componentes padrão
]);

class App extends Component {
  componentDidMount() {
    // Adicionar dados de turfeiras ao mapa
    this.props.dispatch(addDataToMap({
      datasets: {
        info: {
          label: 'Turfeiras Tropicais Brasil',
          id: 'turfeiras-brasil'
        },
        data: sampleTurfeirasData
      },
      option: {
        centerMap: true,
        readOnly: false
      },
      config: {
        version: 'v1',
        config: {
          visState: {
            layers: [
              {
                id: 'turfeiras-layer',
                type: 'point',
                config: {
                  dataId: 'turfeiras-brasil',
                  label: 'Turfeiras',
                  columns: {
                    lat: 'latitude',
                    lng: 'longitude'
                  },
                  isVisible: true,
                  color: [34, 139, 34], // Verde para turfeiras
                  radius: 10
                }
              }
            ]
          }
        }
      }
    }));
  }

  render() {
    return (
      <div style={{position: 'absolute', width: '100%', height: '100%'}}>
        <KeplerGlWithTurfeiras
          id="map"
          width={window.innerWidth}
          height={window.innerHeight}
          mapboxApiAccessToken="pk.demo" // Token de demo
          theme="dark"
          appName="🌿 TurfeirasBrasil"
          version="v1"
          sidePanelWidth={300}
          mapState={{
            latitude: -15.7801,
            longitude: -47.9292,
            zoom: 4
          }}
        />
      </div>
    );
  }
}

const mapStateToProps = state => state;
const dispatchToProps = dispatch => ({dispatch});

export default connect(mapStateToProps, dispatchToProps)(App);
