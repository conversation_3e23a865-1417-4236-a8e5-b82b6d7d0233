# Tutorial: Spatial Data Analysis using Kepler.gl AI Assistant

This tutorial will guide you through the process of using Kepler.gl AI Assistant to perform spatial data analysis.

The spatial data analysis tools are powered by [Geoda](https://geodacenter.github.io/geoda-lib/), which is a free and open source software tool that serves as an introduction to spatial data science for students and researchers. To make it easier for users to go through the spatial data analysis features, we will try to replicate the [Geoda workbook](https://geodacenter.github.io/documentation.html) using Kepler.gl AI Assistant.

## Table of Contents

#### [Get Started](./get-started.md)

#### [Spatial Data Wrangling - Basic Operations](./spatial-data-wragling.md)

#### [Spatial Data Wrangling - GIS Operations](./spatial-data-gis.md)

#### [Basic Mapping](./basic-mapping.md)

#### [Rate Mapping](./rate-mapping.md)

#### [Exploratory Data Analysis](./exploratory-data-analysis.md)

- [Univariate and Bivariate Analysis](./univariate-and-bivariate-analysis.md)
- [Multivariate Analysis](./multivariate-analysis.md)

#### [Contiguity-Based Spatial Weights](./contiguity-based-spatial-weights.md)

#### [Distance-Based Spatial Weights](./distance-based-spatial-weights.md)

#### [Global Spatial Autocorrelation](./global-spatial-autocorrelation.md)

- [Moran Scatter Plot](./moran-scatter-plot.md)
- Correlogram
- Bivariate, Differential and Empirical Bayes

#### [Local Spatial Autocorrelation](./local-spatial-autocorrelation.md)

- [LISA and Local Moran](./local-moran-i.md)
- Other Local Spatial Autorrelation
- Multivariate Local Spatial Autorrelation
- LISA for Discrete Variables

#### [Spatial Regression](./spatial-regression.md)

- [Ordinary Least Squares Regression](./ordinary-least-squares-regression.md)
- [Regression Diagnostics](./regression-diagnostics.md)
- [Spatial Lag Model](./spatial-lag-model.md)
- [Spatial Error Model](./spatial-error-model.md)
