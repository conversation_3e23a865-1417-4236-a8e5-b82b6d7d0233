# Layer Attributes

| Layer Attribute                    | Description                                                                                                                                            | Available in                                   |
| ---------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------- |
| Color/Color based on               | Choose the color of your layer or assign color based on a field from your dataset(s).                                                                  | All layers                                     |
| High-precision rendering           | Activate high-precision rendering when zooming in closely on a layer. High-precision rendering sometimes results in a performance cost.                | Point, Arc, Line, Icon, GeoJSON, Hexagon, Grid |
| Radius/Radius based on             | Change the radius of points or assign radius values based on a field from your dataset(s).                                                             | Point, Icon, GeoJSON                           |
| Opacity                            | Change the transparency of a layer. 1 = opaque, 0 = invisible.                                                                                         | All layers                                     |
| Cluster size                       | Change the granularity of clusters. The lower the numerical value, the smaller the geospatial radius that will be used to aggregate clusters.          | Cluster                                        |
| Radius range                       | Set a lower and upper threshold for projected radius size.                                                                                             | Point, Icon, Geojson, Cluster                  |
| Stroke width/stroke width based on | Change the thickness of lines and arcs, or assign a width based on a field from your dataset(s).                                                       | Arc, line, Geojson                             |
| Stroke width range                 | Set a lower and upper threshold for projected stroke width.                                                                                            | Arc, Line, Geojson                             |
| Grid size                          | Change the number of square kilometers covered by each grid square.                                                                                    | Grid                                           |
| Color Palette                      | Choose from multiple, predefined or customized color palettes to apply to your layer. Predefined palettes are either Uber or ColorBrewer colors.       | All layers                                     |
| Color Scale                        | Choose either a quantile or quantized color scale. A quantile color scale is determined by rank, while a quantized color scale is determined by value. | All layers                                     |
| Height based on                    | Assign grid square height based on a field from your dataset.                                                                                          | Grid, Hexagon, S2                              |
| Filter by count percentile         | Increase or decrease the number of grid squares by choosing a range of percentiles to display.                                                         | Grid, Hexagon                                  |
| Coverage                           | Change what portion of each grid cell is covered by a color square.                                                                                    | Grid, Hexagon                                  |
| Height Scale                       | Change the height of the grid squares, hexagons or S2 when in 3D mode.                                                                                 | Grid, Hexagon, S2                              |
| Stroked                            | When activated, draws outlines around geoshapes.                                                                                                       | GeoJSON, Point                                 |
| Filled                             | When activated, geo shapes are filled in with colors.                                                                                                  | GeoJSON                                        |
| Extruded                           | In 3D mode, assign polygon height values based on some value from your dataset.                                                                        | GeoJSON                                        |
| Wireframe                          | Create outlines around extruded polygons.                                                                                                              | GeoJSON                                        |
| Stroke or radius based on          | Control the radius/thickness of GeoJSON line and point features.                                                                                       | GeoJSON                                        |

[Back to table of contents](README.md)
