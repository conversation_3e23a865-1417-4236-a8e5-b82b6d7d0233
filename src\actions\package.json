{"name": "@kepler.gl/actions", "author": "<PERSON> <<EMAIL>>", "version": "3.1.8", "description": "kepler.gl constants used by kepler.gl components, actions and reducers", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "keywords": ["babel", "es6", "react", "webgl", "visualization", "deck.gl"], "repository": {"type": "git", "url": "https://github.com/keplergl/kepler.gl.git"}, "scripts": {"build": "rm -fr dist && babel src --out-dir dist --source-maps inline --extensions '.ts,.tsx,.js,.jsx' --ignore '**/*.d.ts'", "build:umd": "NODE_OPTIONS=--openssl-legacy-provider webpack --config ./webpack/umd.js --progress --env.prod", "build:types": "tsc --project ./tsconfig.production.json", "prepublishOnly": "babel-node ../../scripts/license-header/bin --license ../../FILE-HEADER && yarn build && yarn build:types", "stab": "mkdir -p dist && touch dist/index.js"}, "files": ["dist", "umd"], "dependencies": {"@deck.gl/core": "^8.9.27", "@kepler.gl/cloud-providers": "3.1.8", "@kepler.gl/constants": "3.1.8", "@kepler.gl/layers": "3.1.8", "@kepler.gl/processors": "3.1.8", "@kepler.gl/table": "3.1.8", "@kepler.gl/types": "3.1.8", "@kepler.gl/utils": "3.1.8", "@reduxjs/toolkit": "^1.7.2", "@types/lodash": "4.17.5", "@types/react-redux": "^7.1.23", "@types/redux-actions": "^2.6.2", "lodash": "4.17.21", "react-palm": "^3.3.8", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-actions": "^2.2.1"}, "nyc": {"sourceMap": false, "instrument": false}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}