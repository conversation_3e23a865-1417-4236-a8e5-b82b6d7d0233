# Use Node.js 18 com ferramentas de build
FROM node:18-slim
WORKDIR /app

# Instala ferramentas necessárias para compilar dependências nativas
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    git \
    && rm -rf /var/lib/apt/lists/*

# Habilita o corepack para gerenciar o yarn
RUN corepack enable

# Configura o Yarn para usar a versão especificada
RUN corepack prepare yarn@4.4.0 --activate

# Copia package.json e yarn.lock primeiro
COPY package.json yarn.lock ./
COPY examples/demo-app/package.json ./examples/demo-app/

# Instala dependências
RUN yarn install

# Copia o resto do código
COPY . .

# Define o diretório de trabalho para o demo-app
WORKDIR /app/examples/demo-app

# Instala dependências específicas do demo-app
RUN npm install --legacy-peer-deps

# Cria diretório dist se não existir
RUN mkdir -p dist

# Configura variáveis de ambiente para evitar problemas no container
ENV DOCKER_CONTAINER=true
ENV NODE_ENV=development
ENV PORT=8080
ENV HOST=0.0.0.0

# Expõe a porta 8080 (mudando de 8081 para 8080)
EXPOSE 8080

# Define o comando para iniciar o demo-app com configuração específica para Docker
CMD ["node", "esbuild.config.mjs", "--start", "--port=8080", "--host=0.0.0.0"]
