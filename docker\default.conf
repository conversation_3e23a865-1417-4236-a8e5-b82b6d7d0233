server {
    listen 80;
    server_name localhost;
    
    # Configurações para SPA (Single Page Application)
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }
    
    # Configurações para arquivos estáticos
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Configurações para API proxy (desabilitado temporariamente)
    # location /api/ {
    #     proxy_pass http://ai-engine:5000/;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header X-Forwarded-Proto $scheme;
    # }
    
    # Configurações para uploads
    location /uploads/ {
        alias /usr/share/nginx/html/uploads/;
        autoindex on;
    }
    
    # Configurações para dados
    location /data/ {
        alias /usr/share/nginx/html/data/;
        autoindex on;
    }
    
    # Configurações de segurança (relaxadas para desenvolvimento)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' http: https: data: blob: ws: wss:" always;
}
