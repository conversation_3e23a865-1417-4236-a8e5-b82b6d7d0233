version: '3.8'

services:
  turfeiras-app:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: turfeiras-brasil
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./logs:/app/logs:rw
    networks:
      - turfeiras-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.turfeiras.rule=Host(`turfeiras.localhost`)"
      - "traefik.http.services.turfeiras.loadbalancer.server.port=3000"

  # Opcional: Nginx para servir arquivos estáticos
  nginx:
    image: nginx:alpine
    container_name: turfeiras-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./public:/usr/share/nginx/html:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - turfeiras-app
    networks:
      - turfeiras-network

  # Opcional: PostgreSQL para dados persistentes
  postgres:
    image: postgres:15-alpine
    container_name: turfeiras-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=turfeiras
      - POSTGRES_USER=turfeiras_user
      - POSTGRES_PASSWORD=secure_password_here
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - turfeiras-network
    ports:
      - "5432:5432"

networks:
  turfeiras-network:
    driver: bridge

volumes:
  postgres_data:
