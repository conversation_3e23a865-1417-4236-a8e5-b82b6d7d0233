// SPDX-License-Identifier: MIT
// Versão ultra-simplificada do Kepler.gl para Turfeiras

import React from 'react';
import { createRoot } from 'react-dom/client';

// Dados de exemplo das turfeiras brasileiras
const turfeirasData = [
  { id: 1, nome: 'Turfeira do Pantanal Norte', lat: -16.2587, lng: -56.6269, estado: 'Mato Grosso', area: 1250.5 },
  { id: 2, nome: 'Turfeira da Serra do Cipó', lat: -19.2833, lng: -43.5833, estado: 'Minas Gerais', area: 890.3 },
  { id: 3, nome: 'Turfeira da Chapada Diamantina', lat: -12.4333, lng: -41.5167, estado: 'Bahia', area: 1456.7 },
  { id: 4, nome: 'Turfeira Amazônica Central', lat: -3.1190, lng: -60.0261, estado: 'Amazonas', area: 2145.8 },
  { id: 5, nome: 'Turf<PERSON> da Mata Atlântica', lat: -23.8536, lng: -46.1391, estado: 'São Paulo', area: 675.9 }
];

// Componente simples para visualização
function TurfeirasApp() {
  const [selectedTurfeira, setSelectedTurfeira] = React.useState(null);

  return (
    <div style={{
      position: 'absolute',
      width: '100%',
      height: '100%',
      background: 'linear-gradient(135deg, #1e3a2e 0%, #2d5a3d 100%)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      display: 'flex'
    }}>
      {/* Sidebar */}
      <div style={{
        width: '350px',
        background: 'rgba(0,0,0,0.3)',
        padding: '20px',
        overflow: 'auto'
      }}>
        <h1 style={{ margin: '0 0 20px 0', fontSize: '24px' }}>
          🌿 Turfeiras Brasil
        </h1>
        <p style={{ margin: '0 0 30px 0', opacity: 0.8, fontSize: '14px' }}>
          Sistema de Análise Geoespacial para Turfeiras Tropicais
        </p>
        
        <h3 style={{ margin: '0 0 15px 0', fontSize: '18px' }}>
          📊 Estatísticas Gerais
        </h3>
        <div style={{ marginBottom: '30px' }}>
          <div style={{ margin: '10px 0', padding: '10px', background: 'rgba(255,255,255,0.1)', borderRadius: '5px' }}>
            <strong>Total de Turfeiras:</strong> {turfeirasData.length}
          </div>
          <div style={{ margin: '10px 0', padding: '10px', background: 'rgba(255,255,255,0.1)', borderRadius: '5px' }}>
            <strong>Área Total:</strong> {turfeirasData.reduce((sum, t) => sum + t.area, 0).toFixed(1)} ha
          </div>
          <div style={{ margin: '10px 0', padding: '10px', background: 'rgba(255,255,255,0.1)', borderRadius: '5px' }}>
            <strong>Estados Cobertos:</strong> {[...new Set(turfeirasData.map(t => t.estado))].length}
          </div>
        </div>

        <h3 style={{ margin: '0 0 15px 0', fontSize: '18px' }}>
          🗺️ Lista de Turfeiras
        </h3>
        {turfeirasData.map(turfeira => (
          <div
            key={turfeira.id}
            onClick={() => setSelectedTurfeira(turfeira)}
            style={{
              margin: '10px 0',
              padding: '15px',
              background: selectedTurfeira?.id === turfeira.id 
                ? 'rgba(113, 212, 121, 0.3)' 
                : 'rgba(255,255,255,0.1)',
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.2s',
              border: selectedTurfeira?.id === turfeira.id 
                ? '2px solid #71d479' 
                : '2px solid transparent'
            }}
          >
            <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
              {turfeira.nome}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              📍 {turfeira.estado}<br/>
              📏 {turfeira.area} hectares<br/>
              🌐 {turfeira.lat.toFixed(4)}, {turfeira.lng.toFixed(4)}
            </div>
          </div>
        ))}
      </div>

      {/* Mapa placeholder */}
      <div style={{
        flex: 1,
        background: '#1a1a1a',
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        {selectedTurfeira ? (
          <div style={{
            background: 'rgba(0,0,0,0.8)',
            padding: '30px',
            borderRadius: '15px',
            textAlign: 'center',
            maxWidth: '500px'
          }}>
            <h2 style={{ margin: '0 0 20px 0', color: '#71d479' }}>
              {selectedTurfeira.nome}
            </h2>
            <div style={{ fontSize: '16px', lineHeight: '1.6' }}>
              <p><strong>Estado:</strong> {selectedTurfeira.estado}</p>
              <p><strong>Área:</strong> {selectedTurfeira.area} hectares</p>
              <p><strong>Coordenadas:</strong> {selectedTurfeira.lat.toFixed(4)}, {selectedTurfeira.lng.toFixed(4)}</p>
            </div>
            
            <div style={{
              marginTop: '30px',
              padding: '20px',
              background: 'rgba(113, 212, 121, 0.2)',
              borderRadius: '10px',
              border: '1px solid #71d479'
            }}>
              <h3 style={{ margin: '0 0 15px 0', color: '#71d479' }}>
                🗺️ Visualização do Mapa
              </h3>
              <p style={{ margin: 0, opacity: 0.9 }}>
                Em uma implementação completa, aqui seria exibido um mapa interativo 
                com a localização da turfeira selecionada, dados geomorfológicos, 
                análises de solo e outras informações relevantes.
              </p>
            </div>
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            opacity: 0.6
          }}>
            <h2 style={{ margin: '0 0 20px 0' }}>
              🗺️ Selecione uma Turfeira
            </h2>
            <p>
              Clique em uma turfeira na lista ao lado para visualizar<br/>
              suas informações e localização no mapa.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// Renderizar a aplicação
const container = document.getElementById('app');
if (container) {
  const root = createRoot(container);
  root.render(<TurfeirasApp />);
} else {
  console.error('Container #app não encontrado!');
}
