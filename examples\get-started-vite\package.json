{"name": "kepler-app-vite", "version": "0.0.1", "license": "MIT", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@babel/runtime": "^7.12.1", "@deck.gl/aggregation-layers": "^8.9.27", "@deck.gl/core": "^8.9.27", "@deck.gl/extensions": "^8.9.27", "@deck.gl/geo-layers": "^8.9.27", "@deck.gl/layers": "^8.9.27", "@deck.gl/mesh-layers": "^8.9.27", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@kepler.gl/components": "3.1.7", "@kepler.gl/reducers": "3.1.7", "@loaders.gl/arrow": "4.3.2", "@loaders.gl/core": "4.3.2", "@loaders.gl/gltf": "4.3.2", "@loaders.gl/images": "4.3.2", "@loaders.gl/mvt": "4.3.2", "@loaders.gl/parquet": "4.3.2", "@loaders.gl/pmtiles": "4.3.2", "@luma.gl/core": "8.5.21", "@luma.gl/engine": "8.5.21", "@luma.gl/gltools": "8.5.21", "@luma.gl/shadertools": "8.5.21", "@luma.gl/webgl": "8.5.21", "@math.gl/core": "^4.0.0", "@math.gl/web-mercator": "^3.6.2", "apache-arrow": "17.0.0", "gl-matrix": "^3.4.3", "lodash": "4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-scripts": "^5.0.1", "react-virtualized": "^9.22.5", "redux": "^4.2.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash.uniq": "^4", "@types/node": "^22.15.21", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-wasm": "^3.4.1"}, "resolutions": {"apache-arrow": "17.0.0"}}