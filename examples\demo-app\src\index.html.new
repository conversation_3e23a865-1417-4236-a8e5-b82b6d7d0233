<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🌿 TurfeirasBrasil - Kepler.gl</title>
    
    <!--Uber Font-->
    <link
      rel="stylesheet"
      href="https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/uber-fonts/4.0.0/superfine.css"
    />
    
    <!--MapBox css-->
    <link href="https://api.tiles.mapbox.com/mapbox-gl-js/v1.1.1/mapbox-gl.css" rel="stylesheet" />
    
    <!-- CSS Bundle -->
    <link rel="stylesheet" href="./bundle.css" />
    
    <style type="text/css">
      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        background: #1f2937;
        font-family: uber-move-text;
      }

      #root {
        width: 100vw;
        height: 100vh;
      }

      .kepler-gl {
        position: absolute !important;
        width: 100% !important;
        height: 100% !important;
        background: #1f2937 !important;
      }
    </style>
  </head>

  <body>
    <!-- Main App Container -->
    <div id="root"></div>

    <!-- JavaScript Bundle - App compilado com todas as dependências -->
    <script src="./bundle.js"></script>
    
    <script>
      console.log('🌿 TurfeirasBrasil - Iniciando aplicação...');
    </script>
  </body>
</html>
