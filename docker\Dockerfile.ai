# Dockerfile para AI Engine - Sistema Turfeiras V3 (Versão Simplificada)
# Otimizado para compatibilidade com diferentes versões do Debian

# Estágio de build base
FROM python:3.10-slim as base-builder

WORKDIR /build

# Instalar ferramentas essenciais para build
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    git \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Criar e ativar ambiente virtual
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Atualizar pip e instalar wheel
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Estágio de build ML
FROM base-builder as ml-builder

# Instalar dependências do sistema para bibliotecas científicas
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev \
    gdal-bin \
    libgdal-dev \
    libgeos-dev \
    libproj-dev \
    libspatialite-dev \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libhdf5-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Definir variáveis de ambiente para GDAL
ENV GDAL_CONFIG=/usr/bin/gdal-config
ENV CPLUS_INCLUDE_PATH=/usr/include/gdal
ENV C_INCLUDE_PATH=/usr/include/gdal

# Copiar arquivo de requirements
COPY ai-engine/requirements-docker.txt .

# Instalar dependências ML básicas primeiro
RUN pip install --no-cache-dir \
    numpy \
    pandas \
    scikit-learn \
    flask \
    flask-cors \
    gunicorn \
    requests

# Instalar dependências restantes
RUN pip install --no-cache-dir -r requirements-docker.txt

# Estágio final - imagem otimizada
FROM python:3.10-slim

# Instalar apenas dependências runtime essenciais
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    gdal-bin \
    libgdal32 \
    libgeos-c1v5 \
    libproj25 \
    libspatialite7 \
    libjpeg62-turbo \
    libpng16-16 \
    libhdf5-103-1 \
    libxml2 \
    libxslt1.1 \
    zlib1g \
    curl \
    && rm -rf /var/lib/apt/lists/* || \
    # Fallback: instalar apenas pacotes essenciais se alguns falharem
    (apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*)

# Copiar ambiente virtual do estágio ML
COPY --from=ml-builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Definir variáveis de ambiente
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    FLASK_APP=server.py \
    FLASK_ENV=production \
    PORT=5000 \
    NUM_WORKERS=2 \
    MAX_REQUESTS=1000 \
    TIMEOUT=300 \
    WORKER_CLASS=sync

# Criar diretórios necessários
WORKDIR /app
RUN mkdir -p /app/models/saved \
    /app/storage/cache \
    /app/storage/uploads \
    /app/logs \
    /app/temp \
    && chmod -R 777 /app/storage \
    && chmod -R 777 /app/models/saved \
    && chmod -R 777 /app/logs \
    && chmod -R 777 /app/temp

# Copiar código da aplicação
COPY ai-engine/models ./models
COPY ai-engine/server.py .
COPY ai-engine/gunicorn_config.py .
COPY ai-engine/requirements-docker.txt .

# Criar script de inicialização otimizado
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "=== Iniciando AI Engine - Turfeiras V3 ==="\n\
\n\
# Verificar se o Python está funcionando\n\
python -c "print(\"✓ Python funcionando\")" || exit 1\n\
\n\
# Verificar dependências básicas\n\
python -c "import numpy, pandas, sklearn, flask; print(\"✓ Dependências básicas OK\")" || exit 1\n\
\n\
# Verificar se o modelo pode ser importado\n\
python -c "from models.random_forest.geomorph_classifier import GeomorphClassifier; print(\"✓ Modelo importado\")" || {\n\
    echo "⚠ Aviso: Problema ao importar modelo, mas continuando..."\n\
}\n\
\n\
# Verificar se as rotas estão acessíveis\n\
python -c "from server import app; print(\"✓ Servidor configurado\")" || exit 1\n\
\n\
# Iniciar servidor\n\
if [ "$FLASK_ENV" = "development" ]; then\n\
    echo "Iniciando em modo desenvolvimento..."\n\
    python server.py\n\
else\n\
    echo "Iniciando em modo produção..."\n\
    python server.py\n\
fi' > /app/start.sh && chmod +x /app/start.sh

# Expor porta
EXPOSE 5000

# Adicionar healthcheck simples
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Comando para iniciar o servidor
CMD ["./start.sh"]
