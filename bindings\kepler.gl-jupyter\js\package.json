{"name": "keplergl-jupyter", "version": "0.3.7", "description": "This is a simple jupyter widget for kepler.gl, an advanced geo-spatial visualization tool, to render large-scale interactive maps.", "author": "<PERSON>", "license": "MIT", "main": "babel/index.js", "repository": {"type": "git", "url": "https://github.com/keplergl/kepler.gl.git"}, "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "jupyterlab-extension", "widgets", "ipython", "ipywidgets"], "files": ["lib/**/*.js", "dist/*.js", "babel/**"], "scripts": {"start": "NODE_ENV=development webpack --config ./webpack/dev.js --mode development --watch --progress", "clean": "rimraf dist/ && rimraf ../keplergl/static/", "cleanall": "npm run clean && rimraf node_modules/", "prepublishOnly": "NODE_OPTIONS=--openssl-legacy-provider yarn build && yarn build:lab", "build": "NODE_OPTIONS=--openssl-legacy-provider npm run clean && npm run build:lab && webpack --config ./webpack/build.js && jupyter labextension build .", "build:lab": "NODE_OPTIONS=--openssl-legacy-provider rimraf babel/ && mkdir babel && babel lib --out-dir babel", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint lib webpack --fix", "prettier": "prettier --config ./.prettierrc --print-width 80 --single-quote --write lib/**/*.js"}, "devDependencies": {"@babel/cli": "7.4.4", "@babel/core": "^7.12.1", "@babel/plugin-transform-class-properties": "^7.12.1", "@babel/plugin-transform-export-namespace-from": "^7.12.1", "@babel/plugin-transform-modules-commonjs": "^7.12.1", "@babel/plugin-transform-optional-chaining": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-transform-typescript": "^7.16.8", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.16.7", "@jupyterlab/builder": "^4.0.0", "apache-arrow": "^13.0.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.0", "babel-plugin-search-and-replace": "^1.0.0", "babel-plugin-transform-inline-environment-variables": "0.4.3", "eslint": "^5.12.1", "eslint-config-prettier": "^3.6.0", "eslint-config-uber-es2015": "^3.1.2", "eslint-config-uber-jsx": "^3.3.3", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-react": "~7.12.4", "html-webpack-plugin": "^4.3.0", "react-dev-utils": "^10.2.1", "rimraf": "^2.6.1", "webpack": "^4.29.0", "webpack-cli": "^3.2.1", "webpack-dev-middleware": "^3.5.1", "webpack-dev-server": "^3.1.14", "webpack-hot-middleware": "^2.24.3", "webpack-stats-plugin": "^0.2.1"}, "dependencies": {"@jupyter-widgets/base": "^6", "@jupyterlab/builder": "^4.0.0", "@kepler.gl/actions": "^3.0.0", "@kepler.gl/components": "^3.0.0", "@kepler.gl/processors": "^3.0.0", "@kepler.gl/reducers": "^3.0.0", "@kepler.gl/schemas": "^3.0.0", "@kepler.gl/styles": "^3.0.0", "@loaders.gl/arrow": "^4.1.0", "@loaders.gl/core": "^4.1.0", "@loaders.gl/csv": "^4.1.0", "@loaders.gl/json": "^4.1.0", "global": "^4.3.0", "node-polyfill-webpack-plugin": "^1.1.2", "querystring": "0.2.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.0.2", "react-dom": "^18.2.0", "react-helmet": "^5.2.0", "react-intl": "^6.3.0", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-actions": "^2.2.1", "styled-components": "6.1.8"}, "resolutions": {"@deck.gl/core": "^8.9.27", "@deck.gl/react": "^8.9.27", "@deck.gl/aggregation-layers": "^8.9.27", "@deck.gl/geo-layers": "^8.9.27", "@deck.gl/layers": "^8.9.27"}, "jupyterlab": {"extension": "babel/labplugin", "outputDir": "../keplergl-jupyter/labextension", "sharedPackages": {"@jupyter-widgets/base": {"bundled": false, "singleton": true}}}, "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}