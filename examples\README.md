# Examples

A list of examples to demonstrate adding `kepler.gl` to your app. Each of the examples is a complete project that can be ran locally.

To start each example, cd into the folder then run:

```
yarn && yarn start
```

- ### [Demo App][demo-app]

  kepler.gl as a single page app, loading sample maps from remote url, saving map data to dropbox. This is also the source code of kepler.gl/#/demo.

- ### [Open Modal][open-modal]
  Open kepler.gl in a modal.

- ### [Custom Reducer][custom-reducer]
  Customize kepler.gl reducer initial state, adding more actions using plugin.

- ### [umd client][umd-client]
  A single html file loading kepler.gl

- ### [Replace UI Component][replace-component]
  Example showing how to replace kepler.gl default ui components using `injectComponents` method.

- ### [Custom theme][custom-theme]
  Customize kepler.gl theme by override default style properties.

- ### [Node App][node-app]
  Embed Kepler.gl in a node/express/webpack application. 

- ### [Custom map style][custom-map-style]

  Demo how to use kepler.gl with other basemap services other than Mapbox.
[custom-reducer]: custom-reducer/README.md
[demo-app]: demo-app/README.md
[node-app]: node-app/README.md

[open-modal]: open-modal/README.md
[umd-client]: umd-client/README.md
[replace-component]: replace-component/README.md
[custom-theme]: custom-theme/README.md
[custom-map-style]: custom-map-style/README.md
