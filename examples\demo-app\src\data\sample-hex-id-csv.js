// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

export default `hex_id,value
89283082c2fffff,64
8928308288fffff,73
89283082c07ffff,65
89283082817ffff,74
89283082c3bffff,66
89283082883ffff,76
89283082c03ffff,60
89283082807ffff,68
8928308289bffff,49
89283082c0fffff,41
89283082c87ffff,50
89283082d4fffff,45
89283082c77ffff,41
89283082c2bffff,53
89283082803ffff,41
89283082813ffff,43
89283082d5bffff,45
89283082897ffff,40
89283082c67ffff,42
89283082d47ffff,51
89283082dc3ffff,52
89283082c33ffff,43
89283082c23ffff,40
89283082887ffff,36
89283082d4bffff,36
892830828bbffff,48
892830828b7ffff,28
89283082c17ffff,34
89283082c6fffff,21
8928308288bffff,25
892830828abffff,26
89283082c27ffff,27
89283082c8fffff,33
89283082cafffff,29
89283082c13ffff,27
89283082cabffff,22
89283082c63ffff,26
89283082d43ffff,30
89283082d53ffff,19
892830828a3ffff,28
89283082d1bffff,20
89283095367ffff,17
8928309536bffff,26
89283082c37ffff,16
89283082c73ffff,17
89283082c8bffff,15
89283082ca7ffff,27
89283082cb3ffff,32
89283082c0bffff,26
89283082ca3ffff,19
89283082dcfffff,18
89283082c1bffff,20
89283082ddbffff,18
8928309534fffff,16
89283082d03ffff,15
89283082cbbffff,21
89283082cd7ffff,9
8928309534bffff,9
892830828c7ffff,13
89283082cc7ffff,12
89283082d0bffff,19
89283082dcbffff,19
89283082dd3ffff,15
89283082dd7ffff,15
892830828d7ffff,13
89283082d17ffff,5
8928309536fffff,8
89283095373ffff,6
89283082cb7ffff,15
89283082d83ffff,9
89283082d07ffff,4
89283082d0fffff,3
89283082d13ffff,6
89283082d9bffff,5
89283082c83ffff,11
89283082d8bffff,4
89283082dc7ffff,5
89283095377ffff,5
89283082c97ffff,4
89283082d7bffff,2
89283082d8fffff,1
89283095347ffff,3
89283095363ffff,2
8928309537bffff,4
89283082d93ffff,6
89283082d73ffff,1
8928309530bffff,1
8928309532bffff,1`;

export const config = {
  version: 'v1',
  config: {
    visState: {
      filters: [],
      layers: [
        {
          id: 'avlgol',
          type: 'hexagonId',
          config: {
            dataId: 'h3-hex-id',
            label: 'H3 Hexagon',
            color: [241, 92, 23],
            columns: {
              hex_id: 'hex_id'
            },
            isVisible: true,
            visConfig: {
              opacity: 0.8,
              colorRange: {
                name: 'Global Warming',
                type: 'sequential',
                category: 'Uber',
                colors: ['#5A1846', '#900C3F', '#C70039', '#E3611C', '#F1920E', '#FFC300']
              },
              coverage: 1,
              sizeRange: [0, 500],
              coverageRange: [0, 1],
              elevationScale: 5
            },
            textLabel: [
              {
                field: null,
                color: [255, 255, 255],
                size: 18,
                offset: [0, 0],
                anchor: 'start',
                alignment: 'center'
              }
            ]
          },
          visualChannels: {
            colorField: {
              name: 'value',
              type: 'integer'
            },
            colorScale: 'quantile',
            sizeField: null,
            sizeScale: 'linear',
            coverageField: null,
            coverageScale: 'linear'
          }
        }
      ],
      interactionConfig: {
        tooltip: {
          fieldsToShow: {
            'h3-hex-id': ['hex_id', 'value']
          },
          enabled: true
        },
        brush: {
          size: 0.5,
          enabled: false
        },
        geocoder: {
          enabled: false
        }
      },
      layerBlending: 'normal',
      splitMaps: [
        {
          layers: {
            avlgol: {
              isAvailable: true,
              isVisible: true
            }
          }
        },
        {
          layers: {
            avlgol: {
              isAvailable: true,
              isVisible: true
            }
          }
        }
      ]
    },
    mapStyle: {
      styleType: 'dark',
      topLayerGroups: {},
      visibleLayerGroups: {
        label: true,
        road: true,
        border: false,
        building: true,
        water: true,
        land: true,
        '3d building': false
      },
      mapStyles: {}
    },
    mapState: {
      isSplit: true
    }
  }
};
