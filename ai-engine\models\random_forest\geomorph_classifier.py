"""
Geomorphology Classifier - Modelo de classificação geomorfológica para turfeiras tropicais

Este módulo implementa um classificador para identificar e categorizar
características geomorfológicas de turfeiras tropicais com base em imagens
de satélite e dados geoespaciais.
"""

import os
import numpy as np
import pandas as pd
import pickle
import logging

# Importações opcionais para funcionalidade completa
try:
    import rasterio
    from rasterio.features import geometry_mask
    RASTERIO_AVAILABLE = True
except ImportError:
    RASTERIO_AVAILABLE = False
    print("Warning: rasterio not available, some functionality will be limited")

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report

# Configuração de logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GeomorphClassifier:
    """
    Classificador de Geomorfologia baseado em Random Forest
    
    Este classificador usa características extraídas de imagens de satélite,
    dados topográficos e outras variáveis ambientais para identificar e
    classificar características geomorfológicas de turfeiras tropicais.
    """
    
    def __init__(self, model_path=None):
        """
        Inicializa o classificador geomorfológico
        
        Args:
            model_path (str, optional): Caminho para o modelo pré-treinado (.pkl)
        """
        self.model = None
        self.features = [
            'ndvi_mean', 'ndvi_std', 'ndwi_mean', 'ndwi_std', 
            'elevation_mean', 'elevation_std', 'slope_mean', 'slope_std',
            'aspect_mean', 'aspect_std', 'curvature_mean', 'curvature_std',
            'twi_mean', 'twi_std', 'distance_to_water',
            'precipitation_annual', 'temperature_mean',
            'soil_organic_carbon', 'soil_moisture', 'drainage_density'
        ]
        
        # Classes geomorfológicas
        self.classes = [
            'Turfeira_Plana',
            'Turfeira_Depressao',
            'Turfeira_Encosta',
            'Turfeira_Vale',
            'Turfeira_Cabeceira',
            'Turfeira_Montanhosa',
            'Turfeira_Costeira'
        ]
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            self.model = RandomForestClassifier(
                n_estimators=200,
                max_depth=20,
                min_samples_split=10,
                min_samples_leaf=5,
                random_state=42,
                n_jobs=-1
            )
            logger.info("Initialized new geomorphology model. Training required.")
    
    def load_model(self, model_path):
        """
        Carrega um modelo pré-treinado
        
        Args:
            model_path (str): Caminho para o arquivo do modelo (.pkl)
        """
        try:
            with open(model_path, 'rb') as f:
                self.model = pickle.load(f)
            logger.info(f"Geomorphology model loaded successfully from {model_path}")
        except Exception as e:
            logger.error(f"Error loading geomorphology model: {e}")
            self.model = RandomForestClassifier(
                n_estimators=200,
                max_depth=20,
                min_samples_split=10,
                min_samples_leaf=5,
                random_state=42,
                n_jobs=-1
            )
    
    def save_model(self, model_path):
        """
        Salva o modelo treinado
        
        Args:
            model_path (str): Caminho para salvar o modelo (.pkl)
        """
        if self.model:
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            with open(model_path, 'wb') as f:
                pickle.dump(self.model, f)
            logger.info(f"Geomorphology model saved to {model_path}")
        else:
            logger.error("No geomorphology model to save")
    
    def train(self, X, y):
        """
        Treina o classificador com dados rotulados
        
        Args:
            X (numpy.ndarray): Matriz de características (n_samples, n_features)
            y (numpy.ndarray): Vetor de rótulos (n_samples,)
        
        Returns:
            dict: Métricas de desempenho do treinamento
        """
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y)
        
        self.model.fit(X_train, y_train)
        
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"Geomorphology model trained with accuracy: {accuracy:.4f}")
        
        feature_importance = dict(zip(
            self.features, 
            self.model.feature_importances_
        ))
        
        return {
            'accuracy': accuracy,
            'feature_importance': feature_importance,
            'classification_report': classification_report(y_test, y_pred, target_names=self.classes)
        }
    
    def predict(self, features):
        """
        Faz predições usando o modelo treinado
        
        Args:
            features (numpy.ndarray): Matriz de características (n_samples, n_features)
        
        Returns:
            numpy.ndarray: Classes preditas
            numpy.ndarray: Probabilidades de cada classe
        """
        if self.model is None:
            logger.error("Geomorphology model not trained or loaded")
            return None, None
        
        predictions = self.model.predict(features)
        probabilities = self.model.predict_proba(features)
        
        return predictions, probabilities
    
    def classify_geomorphology(self, geometry, raster_data=None):
        """
        Classifica a geomorfologia de uma área específica
        
        Args:
            geometry (dict): GeoJSON da geometria
            raster_data (dict, optional): Dados raster para análise
        
        Returns:
            dict: Resultado da classificação geomorfológica
        """
        try:
            # Extrair características da geometria
            features = self._extract_features(geometry, raster_data)
            
            if features is None:
                return {
                    'error': 'Não foi possível extrair características',
                    'class': 'Desconhecido',
                    'confidence': 0.0
                }
            
            # Fazer predição
            predictions, probabilities = self.predict(features)
            
            if predictions is None:
                return {
                    'error': 'Modelo não treinado',
                    'class': 'Desconhecido',
                    'confidence': 0.0
                }
            
            # Obter a classe com maior probabilidade
            predicted_class = predictions[0]
            max_probability = np.max(probabilities[0])
            
            # Criar mapeamento de classes para probabilidades
            class_probabilities = {}
            for i, class_name in enumerate(self.classes):
                class_probabilities[class_name] = float(probabilities[0][i])
            
            return {
                'class': predicted_class,
                'confidence': float(max_probability),
                'probabilities': class_probabilities,
                'geomorphology_type': self._get_geomorphology_description(predicted_class),
                'characteristics': self._get_characteristics(predicted_class)
            }
            
        except Exception as e:
            logger.error(f"Error in geomorphology classification: {e}")
            return {
                'error': str(e),
                'class': 'Desconhecido',
                'confidence': 0.0
            }
    
    def _extract_features(self, geometry, raster_data=None):
        """
        Extrai características geomorfológicas de uma geometria
        
        Args:
            geometry (dict): GeoJSON da geometria
            raster_data (dict, optional): Dados raster adicionais
        
        Returns:
            numpy.ndarray: Vetor de características
        """
        try:
            # Simular extração de características (em produção, usar dados reais)
            features = []
            
            # Características de vegetação (NDVI)
            features.extend([0.65, 0.15])  # ndvi_mean, ndvi_std
            
            # Características de água (NDWI)
            features.extend([0.25, 0.08])  # ndwi_mean, ndwi_std
            
            # Características topográficas
            features.extend([1850.0, 125.0])  # elevation_mean, elevation_std
            features.extend([8.5, 6.2])      # slope_mean, slope_std
            features.extend([180.0, 90.0])   # aspect_mean, aspect_std
            features.extend([0.02, 0.15])    # curvature_mean, curvature_std
            
            # Índice topográfico de umidade
            features.extend([12.5, 3.2])     # twi_mean, twi_std
            
            # Distância à água
            features.append(250.0)           # distance_to_water
            
            # Características climáticas
            features.extend([1850.0, 18.5])  # precipitation_annual, temperature_mean
            
            # Características do solo
            features.extend([4.2, 0.35])     # soil_organic_carbon, soil_moisture
            
            # Densidade de drenagem
            features.append(1.8)             # drainage_density
            
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return None
    
    def _get_geomorphology_description(self, class_name):
        """
        Retorna descrição da classe geomorfológica
        
        Args:
            class_name (str): Nome da classe
        
        Returns:
            str: Descrição da geomorfologia
        """
        descriptions = {
            'Turfeira_Plana': 'Turfeira localizada em área plana com pouca declividade',
            'Turfeira_Depressao': 'Turfeira em depressão natural com acúmulo de água',
            'Turfeira_Encosta': 'Turfeira em encosta com declive moderado',
            'Turfeira_Vale': 'Turfeira localizada em vale com drenagem natural',
            'Turfeira_Cabeceira': 'Turfeira em cabeceira de drenagem',
            'Turfeira_Montanhosa': 'Turfeira em região montanhosa com alta altitude',
            'Turfeira_Costeira': 'Turfeira próxima à costa com influência marinha'
        }
        
        return descriptions.get(class_name, 'Geomorfologia não definida')
    
    def _get_characteristics(self, class_name):
        """
        Retorna características específicas da classe geomorfológica
        
        Args:
            class_name (str): Nome da classe
        
        Returns:
            dict: Características da classe
        """
        characteristics = {
            'Turfeira_Plana': {
                'slope_range': '0-5°',
                'drainage': 'Pobre',
                'water_retention': 'Alta',
                'carbon_stock': 'Muito Alto',
                'vulnerability': 'Baixa'
            },
            'Turfeira_Depressao': {
                'slope_range': '0-3°',
                'drainage': 'Muito Pobre',
                'water_retention': 'Muito Alta',
                'carbon_stock': 'Extremamente Alto',
                'vulnerability': 'Muito Baixa'
            },
            'Turfeira_Encosta': {
                'slope_range': '5-15°',
                'drainage': 'Moderada',
                'water_retention': 'Moderada',
                'carbon_stock': 'Alto',
                'vulnerability': 'Moderada'
            },
            'Turfeira_Vale': {
                'slope_range': '3-8°',
                'drainage': 'Boa',
                'water_retention': 'Moderada',
                'carbon_stock': 'Alto',
                'vulnerability': 'Baixa'
            },
            'Turfeira_Cabeceira': {
                'slope_range': '8-20°',
                'drainage': 'Boa',
                'water_retention': 'Baixa',
                'carbon_stock': 'Moderado',
                'vulnerability': 'Alta'
            },
            'Turfeira_Montanhosa': {
                'slope_range': '15-30°',
                'drainage': 'Excessiva',
                'water_retention': 'Muito Baixa',
                'carbon_stock': 'Baixo',
                'vulnerability': 'Muito Alta'
            },
            'Turfeira_Costeira': {
                'slope_range': '0-8°',
                'drainage': 'Variável',
                'water_retention': 'Variável',
                'carbon_stock': 'Moderado',
                'vulnerability': 'Alta'
            }
        }
        
        return characteristics.get(class_name, {})
    
    def get_feature_importance(self):
        """
        Retorna a importância de cada característica no modelo
        
        Returns:
            dict: Mapeamento de nomes de características para valores de importância
        """
        if self.model is None or not hasattr(self.model, 'feature_importances_'):
            logger.error("Geomorphology model not trained or doesn't support feature importance")
            return {}
        
        return dict(zip(self.features, self.model.feature_importances_))
    
    def extract_features_from_geotiff(self, geometry, raster_paths):
        """
        Extrai características de arquivos GeoTIFF para um polígono
        
        Args:
            geometry (dict): GeoJSON da geometria
            raster_paths (dict): Caminhos para os arquivos raster por banda/índice
        
        Returns:
            numpy.ndarray: Matriz de características
        """
        if not RASTERIO_AVAILABLE:
            logger.warning("Rasterio not available, returning simulated features")
            # Retornar características simuladas se rasterio não estiver disponível
            features = []
            for _ in raster_paths:
                features.extend([0.5, 0.1])  # valores simulados
            return np.array(features).reshape(1, -1)
        
        features = []
        
        for feature_name, raster_path in raster_paths.items():
            if not os.path.exists(raster_path):
                logger.error(f"Raster file not found: {raster_path}")
                continue
                
            try:
                with rasterio.open(raster_path) as src:
                    # Criar máscara da geometria
                    mask = geometry_mask(
                        [geometry],
                        out_shape=(src.height, src.width),
                        transform=src.transform,
                        invert=True
                    )
                    
                    # Ler dados da banda
                    band = src.read(1)
                    
                    # Extrair estatísticas para a região de interesse
                    masked_band = band[mask]
                    
                    if len(masked_band) > 0:
                        mean_value = np.mean(masked_band)
                        std_value = np.std(masked_band)
                        features.extend([mean_value, std_value])
                    else:
                        features.extend([0, 0])
                        
            except Exception as e:
                logger.error(f"Error processing raster {raster_path}: {e}")
                features.extend([0, 0])
        
        return np.array(features).reshape(1, -1)


# Função auxiliar para inicializar o classificador
def create_geomorph_classifier(model_path=None):
    """
    Cria uma instância do classificador geomorfológico
    
    Args:
        model_path (str, optional): Caminho para o modelo pré-treinado
    
    Returns:
        GeomorphClassifier: Instância do classificador
    """
    return GeomorphClassifier(model_path=model_path)


# Exemplo de uso
if __name__ == "__main__":
    # Criar instância do classificador
    classifier = GeomorphClassifier()
    
    # Exemplo de geometria (polígono em formato GeoJSON)
    example_geometry = {
        "type": "Polygon",
        "coordinates": [[
            [-45.0, -22.0],
            [-45.0, -22.1],
            [-45.1, -22.1],
            [-45.1, -22.0],
            [-45.0, -22.0]
        ]]
    }
    
    # Classificar geomorfologia
    result = classifier.classify_geomorphology(example_geometry)
    
    print("Resultado da classificação geomorfológica:")
    print(f"Classe: {result['class']}")
    print(f"Confiança: {result['confidence']:.2f}")
    print(f"Descrição: {result['geomorphology_type']}")
    print("Características:")
    for key, value in result['characteristics'].items():
        print(f"  {key}: {value}")