# Customize kepler.gl Theme

This example show how to customize Kepler.gl theme
  1. Define an object (theme) to override Kepler.gl style
  2. Pass the newly created object as prop to KeplerGl react component

#### 1. Install

```sh
npm install
```

or

```sh
yarn
```


#### 2. Mapbox Token
add mapbox access token to node env

```sh
export MapboxAccessToken=<your_mapbox_token>
```

#### 3. Start the app

```sh
npm start
```
