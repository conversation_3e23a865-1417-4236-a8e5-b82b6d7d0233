# Dockerfile para Kepler.gl + Turfeiras V3 - Sem Nginx
# Servindo diretamente com Node.js + Express

# Estágio de build
FROM node:18-alpine as builder

WORKDIR /app

# Instalar dependências do sistema necessárias para compilação
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    pkgconfig \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    pixman-dev \
    mesa-dev \
    libxi-dev \
    libxext-dev \
    libx11-dev \
    xvfb \
    && rm -rf /var/cache/apk/*

# Habilitar Corepack e configurar Yarn
RUN corepack enable && corepack prepare yarn@4.4.0 --activate

# Copiar package.json e yarn.lock
COPY package.json yarn.lock ./
COPY .yarnrc.yml ./

# Definir variáveis de ambiente para build headless
ENV DISPLAY=:99
ENV NODE_ENV=production
ENV HEADLESS_GL_FORCE_SOFTWARE=1
ENV LIBGL_ALWAYS_SOFTWARE=1
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# Instalar dependências do workspace principal
RUN yarn install --network-timeout 300000

# Copiar código fonte
COPY . .

# Buildar a aplicação demo-app diretamente
WORKDIR /app/examples/demo-app

# Executar build da aplicação com esbuild para Docker
RUN node esbuild.docker.config.mjs

# Verificar se o build foi criado
RUN ls -la dist/

# Estágio de produção - Servir com Node.js HTTP nativo
FROM node:18-alpine

WORKDIR /app

# Instalar apenas dependências necessárias para produção
RUN apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Não precisa instalar Express - usando HTTP nativo do Node.js

# Copiar build da aplicação
COPY --from=builder /app/examples/demo-app/dist ./dist
COPY --from=builder /app/examples/demo-app/server.js ./server.js

# Criar diretórios necessários
RUN mkdir -p ./data
RUN mkdir -p ./uploads

# Expor porta
EXPOSE 8080

# Definir variável de ambiente para porta
ENV PORT=8080

# Comando para iniciar servidor
CMD ["node", "server.js"]
