# Dockerfile para Kepler.gl + Turf<PERSON>s FUNCIONANDO
FROM node:18-alpine

# Instalar dependências do sistema
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    bash

WORKDIR /app

# Copiar package.json
COPY package.json yarn.lock* ./

# Instalar dependências globais primeiro
RUN npm install -g yarn@1.22.22

# Configurar Yarn
RUN yarn config set network-timeout 600000

# Instalar dependências do projeto
RUN yarn install --frozen-lockfile || yarn install

# Copiar código fonte
COPY . .

# Instalar dependências do demo-app
WORKDIR /app/examples/demo-app
RUN npm install --legacy-peer-deps

# Voltar para raiz
WORKDIR /app

# Expor porta
EXPOSE 8080

# Comando para iniciar
CMD ["yarn", "start"]
