<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Turfeiras Brasil - Análise Geoespacial</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/redux@4.2.1/dist/redux.min.js"></script>
    <script src="https://unpkg.com/react-redux@8.1.2/dist/react-redux.min.js"></script>
    <script src="https://unpkg.com/styled-components@6.1.8/dist/styled-components.min.js"></script>
    <script src="https://unpkg.com/kepler.gl@3.1.8/umd/keplergl.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
        }
        .header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        #app {
            height: calc(100vh - 120px);
            background: white;
            border-radius: 8px;
            margin: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            font-size: 1.2rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌿 Turfeiras Brasil</h1>
        <p>Sistema de Análise Geoespacial para Turfeiras Tropicais</p>
    </div>
    
    <div id="app">
        <div class="loading">Carregando sistema de análise...</div>
    </div>

    <script>
        // Configuração do Redux Store
        const { combineReducers, createStore, applyMiddleware } = Redux;
        const { Provider } = ReactRedux;
        const { createElement: h } = React;

        // Configurar o reducer do Kepler.gl
        const reducers = combineReducers({
            keplerGl: KeplerGl.keplerGlReducer
        });

        // Middleware do Kepler.gl
        const middlewares = KeplerGl.enhanceReduxMiddleware([]);
        const store = createStore(reducers, {}, applyMiddleware(...middlewares));

        // Dados de exemplo das turfeiras
        const turfeirasData = {
            fields: [
                {name: 'id', type: 'integer'},
                {name: 'nome', type: 'string'},
                {name: 'latitude', type: 'real'},
                {name: 'longitude', type: 'real'},
                {name: 'area_ha', type: 'real'},
                {name: 'carbono_ton', type: 'real'},
                {name: 'estado', type: 'string'},
                {name: 'tipo_vegetacao', type: 'string'},
                {name: 'data_registro', type: 'timestamp'}
            ],
            rows: [
                [1, 'Turfeira Amazônica Norte', -2.5297, -60.0261, 145.5, 2890.3, 'Amazonas', 'Floresta Ombrófila', '2024-01-15'],
                [2, 'Turfeira do Pantanal Sul', -19.2563, -56.6289, 89.2, 1567.8, 'Mato Grosso do Sul', 'Savana Florestada', '2024-02-20'],
                [3, 'Turfeira Cerrado Central', -15.7939, -47.8828, 234.7, 4123.9, 'Distrito Federal', 'Cerrado Stricto Sensu', '2024-03-10'],
                [4, 'Turfeira Mata Atlântica', -22.9068, -43.1729, 67.3, 945.6, 'Rio de Janeiro', 'Mata Atlântica', '2024-04-05'],
                [5, 'Turfeira Caatinga Nordeste', -9.3122, -40.5061, 123.8, 1876.4, 'Pernambuco', 'Caatinga Arbórea', '2024-05-12']
            ]
        };

        // Configuração inicial do Kepler.gl
        const keplerGlConfig = {
            version: 'v1',
            config: {
                visState: {
                    filters: [],
                    layers: [
                        {
                            id: 'turfeiras_pontos',
                            type: 'point',
                            config: {
                                dataId: 'turfeiras_dataset',
                                label: 'Turfeiras Brasileiras',
                                color: [34, 139, 34],
                                columns: {
                                    lat: 'latitude',
                                    lng: 'longitude'
                                },
                                isVisible: true,
                                visConfig: {
                                    radius: 10,
                                    fixedRadius: false,
                                    opacity: 0.8,
                                    outline: false,
                                    thickness: 2,
                                    strokeColor: [255, 255, 255],
                                    colorRange: {
                                        name: 'Global Warming',
                                        type: 'sequential',
                                        category: 'Uber',
                                        colors: ['#5A1846', '#900C3F', '#C70039', '#E3611C', '#F1920E', '#FFC300']
                                    },
                                    strokeColorRange: {
                                        name: 'Global Warming',
                                        type: 'sequential',
                                        category: 'Uber',
                                        colors: ['#5A1846', '#900C3F', '#C70039', '#E3611C', '#F1920E', '#FFC300']
                                    },
                                    radiusRange: [0, 50],
                                    filled: true
                                }
                            }
                        }
                    ],
                    interactionConfig: {
                        tooltip: {
                            fieldsToShow: {
                                'turfeiras_dataset': [
                                    {name: 'nome', format: null},
                                    {name: 'estado', format: null},
                                    {name: 'area_ha', format: '.2f'},
                                    {name: 'carbono_ton', format: '.1f'},
                                    {name: 'tipo_vegetacao', format: null}
                                ]
                            },
                            enabled: true
                        },
                        brush: {enabled: false, size: 0.5},
                        geocoder: {enabled: false},
                        coordinate: {enabled: false}
                    },
                    layerBlending: 'normal',
                    splitMaps: [],
                    animationConfig: {currentTime: null, speed: 1}
                },
                mapState: {
                    bearing: 0,
                    dragRotate: false,
                    latitude: -14.235,
                    longitude: -51.9253,
                    pitch: 0,
                    zoom: 4,
                    isSplit: false
                },
                mapStyle: {
                    styleType: 'dark',
                    topLayerGroups: {},
                    visibleLayerGroups: {
                        label: true,
                        road: true,
                        border: false,
                        building: true,
                        water: true,
                        land: true,
                        '3d building': false
                    },
                    threeDBuildingColor: [9.665468314072013, 17.18305478057247, 31.1442867897876],
                    mapStyles: {}
                }
            }
        };

        // Componente principal
        function TurfeirasApp() {
            React.useEffect(() => {
                // Carregar dados automaticamente quando o componente montar
                const loadData = () => {
                    store.dispatch(KeplerGl.addDataToMap({
                        datasets: {
                            info: {
                                label: 'Turfeiras Brasileiras',
                                id: 'turfeiras_dataset'
                            },
                            data: turfeirasData
                        },
                        option: {
                            centerMap: true,
                            readOnly: false,
                            keepExistingConfig: false
                        },
                        config: keplerGlConfig.config
                    }));
                };

                // Carregar dados após um pequeno delay
                setTimeout(loadData, 1000);
            }, []);

            return h(Provider, { store }, 
                h(KeplerGl.KeplerGl, {
                    id: "turfeiras-map",
                    width: window.innerWidth - 32,
                    height: window.innerHeight - 120,
                    mapboxApiAccessToken: "pk.eyJ1IjoidHVyZmVpcmFzYnJhc2lsIiwiYSI6ImNscW1naDUyMjE3cHAybHFvZGtpbGpvZDgifQ.demo-token-replace-with-real", // Você precisa substituir por um token real
                    appName: "Turfeiras Brasil",
                    version: "v1.0",
                    theme: "dark"
                })
            );
        }

        // Renderizar aplicação
        const root = ReactDOM.createRoot(document.getElementById('app'));
        root.render(h(TurfeirasApp));

        // Redimensionar automaticamente
        window.addEventListener('resize', () => {
            // Trigger resize event for Kepler.gl
            window.dispatchEvent(new Event('resize'));
        });
    </script>
</body>
</html>
