# Sistema de IA para Análise Geomorfológica de Turfeiras - V3

Este sistema fornece análise automatizada de características geomorfológicas de turfeiras tropicais usando aprendizado de máquina e processamento de dados geoespaciais.

## 🚀 Funcionalidades

### Classificação Geomorfológica
- **7 Classes de Geomorfologia**: Turfeira Plana, Depressão, Encosta, Vale, Cabeceira, Montanhosa, Costeira
- **20 Características Analisadas**: NDVI, NDWI, elevação, declividade, curvatura, TWI, distância à água, etc.
- **Modelo Random Forest**: Classificador robusto com alta precisão
- **Análise de Confiança**: Probabilidades para cada classe

### APIs REST
- **Classificação Individual**: `/classify-geomorphology`
- **Classificação em Lote**: `/batch-classify`
- **Informações do Modelo**: `/model-info`
- **Tipos de Geomorfologia**: `/geomorphology-types`
- **Importância das Características**: `/feature-importance`

## 📋 Requisitos

### Dependências Principais
```
numpy>=1.24.3
pandas>=2.0.1
scikit-learn>=1.2.2
rasterio>=1.3.6
geopandas>=0.13.0
flask>=2.3.2
```

### Instalação Completa
```bash
pip install -r requirements-ml.txt
```

## 🔧 Instalação e Configuração

### 1. Clonar o Repositório
```bash
git clone <repository-url>
cd TURFEIRASV3/ai-engine
```

### 2. Configurar Ambiente
```bash
# Criar ambiente virtual
python -m venv venv

# Ativar ambiente (Windows)
venv\Scripts\activate

# Ativar ambiente (Linux/Mac)
source venv/bin/activate

# Instalar dependências
pip install -r requirements-ml.txt
```

### 3. Iniciar o Servidor

#### Modo Desenvolvimento
```bash
python server.py
```

#### Modo Produção
```bash
# Linux/Mac
chmod +x start.sh
./start.sh

# Windows
python server.py
```

## 🔍 Uso da API

### Classificação de Geomorfologia
```python
import requests
import json

# Dados de exemplo
data = {
    "geometry": {
        "type": "Polygon",
        "coordinates": [[
            [-45.0, -22.0],
            [-45.0, -22.1],
            [-45.1, -22.1],
            [-45.1, -22.0],
            [-45.0, -22.0]
        ]]
    }
}

# Fazer requisição
response = requests.post(
    'http://localhost:5000/classify-geomorphology',
    json=data
)

result = response.json()
print(f"Classe: {result['class']}")
print(f"Confiança: {result['confidence']}")
```

### Resposta Esperada
```json
{
    "class": "Turfeira_Plana",
    "confidence": 0.85,
    "probabilities": {
        "Turfeira_Plana": 0.85,
        "Turfeira_Depressao": 0.10,
        "Turfeira_Encosta": 0.03,
        "Turfeira_Vale": 0.01,
        "Turfeira_Cabeceira": 0.01,
        "Turfeira_Montanhosa": 0.00,
        "Turfeira_Costeira": 0.00
    },
    "geomorphology_type": "Turfeira localizada em área plana com pouca declividade",
    "characteristics": {
        "slope_range": "0-5°",
        "drainage": "Pobre",
        "water_retention": "Alta",
        "carbon_stock": "Muito Alto",
        "vulnerability": "Baixa"
    },
    "metadata": {
        "timestamp": "2025-01-07T10:30:00Z",
        "model_version": "3.0.0",
        "processing_time": 0.5,
        "data_source": "Satellite Imagery + DEM"
    }
}
```

## 📊 Classes Geomorfológicas

### 1. Turfeira Plana
- **Declividade**: 0-5°
- **Drenagem**: Pobre
- **Retenção de Água**: Alta
- **Estoque de Carbono**: Muito Alto
- **Vulnerabilidade**: Baixa

### 2. Turfeira Depressão
- **Declividade**: 0-3°
- **Drenagem**: Muito Pobre
- **Retenção de Água**: Muito Alta
- **Estoque de Carbono**: Extremamente Alto
- **Vulnerabilidade**: Muito Baixa

### 3. Turfeira Encosta
- **Declividade**: 5-15°
- **Drenagem**: Moderada
- **Retenção de Água**: Moderada
- **Estoque de Carbono**: Alto
- **Vulnerabilidade**: Moderada

### 4. Turfeira Vale
- **Declividade**: 3-8°
- **Drenagem**: Boa
- **Retenção de Água**: Moderada
- **Estoque de Carbono**: Alto
- **Vulnerabilidade**: Baixa

### 5. Turfeira Cabeceira
- **Declividade**: 8-20°
- **Drenagem**: Boa
- **Retenção de Água**: Baixa
- **Estoque de Carbono**: Moderado
- **Vulnerabilidade**: Alta

### 6. Turfeira Montanhosa
- **Declividade**: 15-30°
- **Drenagem**: Excessiva
- **Retenção de Água**: Muito Baixa
- **Estoque de Carbono**: Baixo
- **Vulnerabilidade**: Muito Alta

### 7. Turfeira Costeira
- **Declividade**: 0-8°
- **Drenagem**: Variável
- **Retenção de Água**: Variável
- **Estoque de Carbono**: Moderado
- **Vulnerabilidade**: Alta

## 🧪 Testes

### Executar Testes Unitários
```bash
python test_system.py
```

### Executar Testes de Performance
```bash
python test_system.py
```

### Verificar Saúde do Sistema
```bash
curl http://localhost:5000/health
```

## 🔧 Configuração Avançada

### Variáveis de Ambiente
```bash
# Porta do servidor
export PORT=5000

# Número de workers (produção)
export WORKERS=4

# Timeout em segundos
export TIMEOUT=300

# Modo debug
export DEBUG=false

# Ambiente Flask
export FLASK_ENV=production
```

### Configuração do Gunicorn
O arquivo `gunicorn_config.py` contém configurações otimizadas para produção:
- Workers adaptativos baseados no número de CPUs
- Timeouts estendidos para operações de ML
- Configurações de logging detalhadas
- Configurações de segurança para produção

## 📈 Monitoramento

### Endpoints de Monitoramento
- **GET /health**: Status do sistema
- **GET /model-info**: Informações do modelo
- **GET /feature-importance**: Importância das características

### Métricas de Performance
- **Latência**: ~0.5s por classificação
- **Throughput**: ~100 classificações/s
- **Precisão**: >90% (dados simulados)

## 🔒 Segurança

### Boas Práticas Implementadas
- Validação de entrada de dados
- Sanitização de geometrias
- Tratamento de erros robusto
- Logging de segurança
- Configurações de produção seguras

## 🚀 Deploy

### Docker (Recomendado)
```bash
# Construir imagem
docker build -t turfeiras-ai-v3 .

# Executar container
docker run -p 5000:5000 turfeiras-ai-v3
```

### Produção com Gunicorn
```bash
# Iniciar com configuração de produção
gunicorn --config gunicorn_config.py server:app
```

## 📚 Documentação da API

### Swagger/OpenAPI
Acesse `http://localhost:5000/docs` para documentação interativa da API.

### Endpoints Principais

#### POST /classify-geomorphology
Classifica a geomorfologia de uma área.

**Parâmetros:**
- `geometry`: GeoJSON da geometria
- `raster_data`: Dados raster opcionais

**Resposta:**
- `class`: Classe geomorfológica
- `confidence`: Confiança da classificação
- `probabilities`: Probabilidades de cada classe
- `characteristics`: Características da classe

#### POST /batch-classify
Classifica múltiplas geometrias em lote.

**Parâmetros:**
- `geometries`: Array de geometrias GeoJSON

**Resposta:**
- `results`: Array de resultados
- `total_processed`: Total processado
- `successful`: Sucessos
- `failed`: Falhas

## 🐛 Solução de Problemas

### Problemas Comuns

#### 1. Erro de Importação
```bash
# Verificar se as dependências estão instaladas
pip list | grep -E "(numpy|pandas|scikit-learn|rasterio|flask)"
```

#### 2. Modelo Não Carregado
```bash
# Verificar se o diretório de modelos existe
mkdir -p models/saved
```

#### 3. Timeout na Classificação
```bash
# Aumentar timeout
export TIMEOUT=600
```

## 📞 Suporte

Para suporte e contribuições:
1. Criar issue no repositório
2. Consultar documentação
3. Executar testes para diagnosticar problemas

## 📄 Licença

Este projeto está licenciado sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## 🏆 Créditos

Desenvolvido para análise de turfeiras tropicais brasileiras com foco em:
- Conservação de carbono
- Análise geomorfológica
- Monitoramento ambiental
- Pesquisa científica

---

**Versão**: 3.0.0  
**Data**: Janeiro 2025  
**Status**: Funcional e Testado
