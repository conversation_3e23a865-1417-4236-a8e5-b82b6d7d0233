# Filters

Add filters to your map to limit the data that is displayed. Filters must be based on the columns in your dataset. 

To add a filter:

1. Select Filters from the right navigation bar.
![select filters](https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/documentation/image1.png "select filters")

2. The Filters panel displays the list of existing filters, color-coded by dataset. To create a new filter, Click __Add Filter__.

3. Choose a dataset, and then a field on which to filter your data. Filter values are defined by field data type (number, string, timestamp, etc.). 
![choose a dataset](https://d1a3f4spazzrp4.cloudfront.net/kepler.gl/documentation/image29.png "choose a dataset")

4. Your filter is applied to your map as soon as you specify the field and value.
5. Delete a filter anytime by clicking the __trashcan__ to the right of the filter you wish to delete.

__Note__: filters apply to all layers in the same dataset on your map.

[Back to table of contents](README.md)
