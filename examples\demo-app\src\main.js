// SPDX-License-Identifier: MIT
// Kepler.gl Turfeiras - Aplicação principal simplificada

import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { createStore, combineReducers, applyMiddleware } from 'redux';
import { taskMiddleware } from 'react-palm/tasks';
import { keplerGlReducer } from '@kepler.gl/reducers';
import KeplerGl from '@kepler.gl/components';
import { addDataToMap } from '@kepler.gl/actions';

// Configuração do Redux Store
const initialState = {};
const reducer = combineReducers({
  keplerGl: keplerGlReducer
});

const store = createStore(reducer, initialState, applyMiddleware(taskMiddleware));

// Dados de exemplo das turfeiras brasileiras
const turfeirasData = {
  fields: [
    { name: 'id', type: 'integer' },
    { name: 'nome', type: 'string' },
    { name: 'latitude', type: 'real' },
    { name: 'longitude', type: 'real' },
    { name: 'area_ha', type: 'real' },
    { name: 'estado', type: 'string' },
    { name: 'tipo_vegetacao', type: 'string' },
    { name: 'status', type: 'string' }
  ],
  rows: [
    [1, 'Turfeira do Pantanal Norte', -16.2587, -56.6269, 1250.5, 'Mato Grosso', 'Gramíneas', 'Preservada'],
    [2, 'Turfeira da Serra do Cipó', -19.2833, -43.5833, 890.3, 'Minas Gerais', 'Campo rupestre', 'Conservada'],
    [3, 'Turfeira da Chapada Diamantina', -12.4333, -41.5167, 1456.7, 'Bahia', 'Cerrado', 'Monitorada'],
    [4, 'Turfeira Amazônica Central', -3.1190, -60.0261, 2145.8, 'Amazonas', 'Floresta', 'Preservada'],
    [5, 'Turfeira da Mata Atlântica', -23.8536, -46.1391, 675.9, 'São Paulo', 'Restinga', 'Protegida'],
    [6, 'Turfeira Caatinga Nordeste', -9.3122, -40.5061, 123.8, 'Pernambuco', 'Caatinga', 'Monitorada'],
    [7, 'Turfeira São Paulo Interior', -23.5489, -46.6388, 78.9, 'São Paulo', 'Mata Atlântica', 'Conservada'],
    [8, 'Turfeira Minas Gerais Sul', -19.9167, -43.9345, 167.2, 'Minas Gerais', 'Cerrado', 'Em Recuperação']
  ]
};

// Componente principal da aplicação
function App() {
  React.useEffect(() => {
    // Carregar dados das turfeiras automaticamente
    store.dispatch(
      addDataToMap({
        datasets: {
          info: {
            label: 'Turfeiras Brasileiras',
            id: 'turfeiras_dataset'
          },
          data: turfeirasData
        },
        option: {
          centerMap: true,
          readOnly: false
        },
        config: {
          version: 'v1',
          config: {
            visState: {
              filters: [],
              layers: [
                {
                  id: 'turfeiras-layer',
                  type: 'point',
                  config: {
                    dataId: 'turfeiras_dataset',
                    label: 'Turfeiras Brasileiras',
                    columns: {
                      lat: 'latitude',
                      lng: 'longitude'
                    },
                    isVisible: true,
                    visConfig: {
                      radius: 15,
                      fixedRadius: false,
                      opacity: 0.8,
                      outline: true,
                      thickness: 2,
                      strokeColor: [255, 255, 255],
                      colorRange: {
                        name: 'Global Warming',
                        type: 'sequential',
                        category: 'Uber',
                        colors: ['#5A1846', '#900C3F', '#C70039', '#E3611C', '#F1920E', '#FFC300']
                      }
                    }
                  }
                }
              ]
            },
            mapState: {
              bearing: 0,
              dragRotate: false,
              latitude: -14.235,
              longitude: -51.9253,
              pitch: 0,
              zoom: 4,
              isSplit: false
            },
            mapStyle: {
              styleType: 'dark',
              topLayerGroups: {},
              visibleLayerGroups: {
                label: true,
                road: true,
                border: false,
                building: true,
                water: true,
                land: true,
                '3d building': false
              }
            }
          }
        }
      })
    );
  }, []);

  return (
    <Provider store={store}>
      <div style={{ position: 'absolute', width: '100%', height: '100%' }}>
        <KeplerGl
          id="map"
          mapboxApiAccessToken={process.env.MapboxAccessToken || 'pk.eyJ1IjoidWJlcmRhdGEiLCJhIjoiY2pudzRtaWloMDAzcTN2bzN1aXdxZHB5bSJ9.2bkfz2lBzd5fcCWEo-XuTA'}
          width={window.innerWidth}
          height={window.innerHeight}
        />
      </div>
    </Provider>
  );
}

// Renderizar a aplicação
const container = document.getElementById('app');
if (container) {
  const root = createRoot(container);
  root.render(<App />);
} else {
  console.error('Container #app não encontrado!');
}
