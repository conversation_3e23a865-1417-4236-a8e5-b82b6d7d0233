// Módulo de dados das turfeiras para Kepler.gl
// Este arquivo será importado no app.js principal

/**
 * Dados das turfeiras brasileiras
 */
export const turfeirasData = {
  fields: [
    { name: 'id', type: 'integer' },
    { name: 'nome', type: 'string' },
    { name: 'latitude', type: 'real' },
    { name: 'longitude', type: 'real' },
    { name: 'area_ha', type: 'real' },
    { name: 'carbono_ton', type: 'real' },
    { name: 'estado', type: 'string' },
    { name: 'tipo_vegetacao', type: 'string' },
    { name: 'status', type: 'string' }
  ],
  rows: [
    [1, 'Turfeira Amazônica Norte', -2.5297, -60.0261, 145.5, 2890.3, 'Amazonas', 'Floresta Ombrófila', 'Conservada'],
    [2, 'Turfeira do Pantanal Sul', -19.2563, -56.6289, 89.2, 1567.8, 'Mato Grosso do Sul', '<PERSON><PERSON>', 'Degradada'],
    [3, 'Turfeira Cerrado Central', -15.7939, -47.8828, 234.7, 4123.9, 'Distrito Federal', 'Cerrado Stric<PERSON>', 'Conservada'],
    [4, 'Turfeira Mata Atlântica', -22.9068, -43.1729, 67.3, 945.6, 'Rio de Janeiro', 'Mata Atlântica', 'Em Recuperação'],
    [5, 'Turfeira Caatinga Nordeste', -9.3122, -40.5061, 123.8, 1876.4, 'Pernambuco', 'Caatinga Arbórea', 'Monitorada'],
    [6, 'Turfeira São Paulo Interior', -23.5489, -46.6388, 78.9, 1234.7, 'São Paulo', 'Mata Atlântica', 'Monitorada'],
    [7, 'Turfeira Minas Gerais', -19.9167, -43.9345, 167.2, 3456.8, 'Minas Gerais', 'Cerrado', 'Conservada'],
    [8, 'Turfeira Bahia Nordeste', -12.9714, -38.5014, 89.4, 1789.2, 'Bahia', 'Caatinga', 'Em Recuperação']
  ]
};

/**
 * Configuração para o Kepler.gl
 */
export const turfeirasConfig = {
  dataId: 'turfeiras-brasileiras',
  label: 'Turfeiras Brasileiras',
  color: [113, 212, 121]
};

/**
 * Função para adicionar dados das turfeiras ao Kepler.gl
 * @param {Function} dispatch - Função dispatch do Redux
 * @param {Object} KeplerGl - Objeto KeplerGl com actions
 */
export const addTurfeirasToMap = (dispatch, KeplerGl) => {
  dispatch(
    KeplerGl.addDataToMap({
      datasets: [
        {
          info: {
            id: turfeirasConfig.dataId,
            label: turfeirasConfig.label
          },
          data: turfeirasData
        }
      ],
      options: {
        centerMap: true,
        readOnly: false
      },
      config: {
        visState: {
          filters: [],
          layers: [
            {
              id: 'turfeiras-layer',
              type: 'point',
              config: {
                dataId: turfeirasConfig.dataId,
                label: turfeirasConfig.label,
                color: turfeirasConfig.color,
                columns: {
                  lat: 'latitude',
                  lng: 'longitude'
                },
                isVisible: true,
                visConfig: {
                  radius: 15,
                  fixedRadius: false,
                  opacity: 0.8,
                  outline: true,
                  thickness: 2,
                  strokeColor: [255, 255, 255],
                  colorRange: {
                    name: 'Global Warming',
                    type: 'sequential',
                    category: 'Uber'
                  },
                  strokeColorRange: {
                    name: 'Global Warming',
                    type: 'sequential',
                    category: 'Uber'
                  }
                }
              }
            }
          ]
        }
      }
    })
  );
};
