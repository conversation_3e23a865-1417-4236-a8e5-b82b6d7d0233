# Configuração do Gunicorn para o servidor de IA - Turfeiras V3
# Arquivo: gunicorn_config.py

import multiprocessing
import os

# Configurações básicas
bind = f"0.0.0.0:{os.environ.get('PORT', 5000)}"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000

# Configurações de timeout
timeout = 120
keepalive = 2

# Configurações de logging
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Configurações de processo
preload_app = True
max_requests = 1000
max_requests_jitter = 50

# Configurações de memória
worker_tmp_dir = "/dev/shm"

# Configurações de segurança
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Configurações específicas para ML
# Aumentar timeout para operações de ML
timeout = 300
graceful_timeout = 30

# Configurações de desenvolvimento
if os.environ.get('DEBUG', 'False').lower() == 'true':
    reload = True
    workers = 1
    loglevel = "debug"
else:
    reload = False

# Configurações de produção
if os.environ.get('ENV', 'development') == 'production':
    # Configurações otimizadas para produção
    workers = min(multiprocessing.cpu_count() * 2, 8)  # Máximo 8 workers
    worker_class = "gevent"
    worker_connections = 1000
    
    # Configurações de logging para produção
    access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s %(p)s'
    
    # Configurações de segurança para produção
    forwarded_allow_ips = "*"
    secure_scheme_headers = {"X-FORWARDED-PROTO": "https"}

def when_ready(server):
    """Callback executado quando o servidor está pronto"""
    server.log.info("Servidor de IA Turfeiras V3 iniciado com sucesso")
    server.log.info(f"Workers: {workers}")
    server.log.info(f"Timeout: {timeout}s")
    server.log.info(f"Bind: {bind}")

def worker_int(worker):
    """Callback executado quando um worker recebe SIGINT"""
    worker.log.info("Worker recebeu SIGINT, finalizando...")

def pre_fork(server, worker):
    """Callback executado antes do fork do worker"""
    server.log.info(f"Worker {worker.pid} sendo criado")

def post_fork(server, worker):
    """Callback executado após o fork do worker"""
    server.log.info(f"Worker {worker.pid} criado com sucesso")

def pre_exec(server):
    """Callback executado antes da execução"""
    server.log.info("Iniciando execução do servidor")

def on_exit(server):
    """Callback executado na saída"""
    server.log.info("Servidor finalizando...")

def on_reload(server):
    """Callback executado no reload"""
    server.log.info("Servidor sendo recarregado...")

# Configurações de ambiente específicas
if os.environ.get('WORKER_CLASS'):
    worker_class = os.environ.get('WORKER_CLASS')

if os.environ.get('WORKERS'):
    workers = int(os.environ.get('WORKERS'))

if os.environ.get('TIMEOUT'):
    timeout = int(os.environ.get('TIMEOUT'))

# Configurações de SSL (se necessário)
if os.environ.get('SSL_CERT') and os.environ.get('SSL_KEY'):
    certfile = os.environ.get('SSL_CERT')
    keyfile = os.environ.get('SSL_KEY')
    ssl_version = 2  # TLS 1.0+
    
    # Configurações adicionais de SSL
    ciphers = "ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS"
    do_handshake_on_connect = True
    suppress_ragged_eofs = True
