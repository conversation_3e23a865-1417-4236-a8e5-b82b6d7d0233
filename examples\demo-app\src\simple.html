<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Kepler.gl Simples</title>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/redux@4.2.1/dist/redux.min.js"></script>
    <script src="https://unpkg.com/react-redux@8.0.5/dist/react-redux.min.js"></script>
    <script src="https://unpkg.com/kepler.gl@3.1.0/umd/keplergl.min.js"></script>
    <link href="https://api.tiles.mapbox.com/mapbox-gl-js/v1.1.1/mapbox-gl.css" rel="stylesheet" />
  </head>

  <body style="margin: 0; padding: 0; overflow: hidden;">
    <div id="app" style="position: absolute; width: 100%; height: 100%; background: rgb(29, 32, 48);"></div>

    <script>
      console.log('Iniciando Kepler.gl...');
      
      // Verificar se o KeplerGl foi carregado
      if (typeof KeplerGl === 'undefined') {
        console.error('KeplerGl não foi carregado!');
        document.getElementById('app').innerHTML = '<h1 style="color: white; text-align: center; margin-top: 50px;">Erro: KeplerGl não carregou</h1>';
      } else {
        console.log('KeplerGl carregado com sucesso!');
        
        // Criar store Redux
        const reducers = Redux.combineReducers({
          keplerGl: KeplerGl.keplerGlReducer
        });
        
        const store = Redux.createStore(reducers, {});
        
        // Componente da aplicação
        const App = () => {
          return React.createElement(KeplerGl.KeplerGl, {
            id: 'map',
            width: window.innerWidth,
            height: window.innerHeight,
            mapboxApiAccessToken: '', // Pode deixar vazio para usar OpenStreetMap
          });
        };
        
        // Renderizar
        const root = ReactDOM.createRoot(document.getElementById('app'));
        root.render(
          React.createElement(ReactRedux.Provider, { store: store },
            React.createElement(App)
          )
        );
        
        console.log('App renderizada!');
      }
    </script>
  </body>
</html>
