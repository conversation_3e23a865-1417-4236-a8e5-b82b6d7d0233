// Servidor HTTP simples para servir arquivos estáticos
// Sem dependências externas - apenas Node.js nativo

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 8080;

// MIME types para diferentes tipos de arquivo
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.woff2': 'application/font-woff2',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.ico': 'image/x-icon'
};

function serveFile(filePath, res) {
  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeType = mimeTypes[extname] || 'application/octet-stream';

  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        // Arquivo não encontrado - servir production.html para SPA
        fs.readFile(path.join(__dirname, 'dist', 'production.html'), (error, content) => {
          if (error) {
            res.writeHead(500);
            res.end('Erro interno do servidor: ' + error.code);
          } else {
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(content, 'utf-8');
          }
        });
      } else {
        res.writeHead(500);
        res.end('Erro interno do servidor: ' + error.code);
      }
    } else {
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content, 'utf-8');
    }
  });
}

const server = http.createServer((req, res) => {
  // Configurar CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url);
  let pathname = `.${parsedUrl.pathname}`;

  // Endpoint de health check
  if (parsedUrl.pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      service: 'turfeiras-kepler'
    }));
    return;
  }

  // Servir arquivos da pasta dist
  if (pathname === './') {
    pathname = './dist/production.html'; // Usar production.html como padrão
  } else if (!pathname.startsWith('./dist/')) {
    pathname = `./dist${parsedUrl.pathname}`;
  }

  // Verificar se o arquivo existe
  fs.stat(pathname, (error, stats) => {
    if (error || !stats.isFile()) {
      // Se não é um arquivo, servir production.html (para SPA routing)
      serveFile('./dist/production.html', res);
    } else {
      serveFile(pathname, res);
    }
  });
});

server.listen(PORT, 'localhost', () => {
  console.log(`🌿 ======================================`);
  console.log(`🌿 SISTEMA TURFEIRAS BRASIL - KEPLER.GL`);
  console.log(`🌿 ======================================`);
  console.log(`🚀 Servidor Turfeiras rodando na porta ${PORT}`);
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log(`🗺️ Sistema: http://localhost:${PORT}/production.html`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🌿 ======================================`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📄 Recebido SIGTERM, encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('📄 Recebido SIGINT, encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});
