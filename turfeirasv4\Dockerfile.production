# Dockerfile Produção - Turfeiras Brasil
FROM node:18-alpine AS builder

# Metadados
LABEL maintainer="TurfeirasBrasil <<EMAIL>>"
LABEL version="1.0"
LABEL description="Sistema de Análise Geoespacial para Turfeiras Tropicais"

# Instalar dependências mínimas
RUN apk add --no-cache git

# Criar usuário não-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

WORKDIR /app

# Copiar apenas arquivos de dependências primeiro (cache layer)
COPY production-server/package*.json ./

# Instalar dependências de produção
RUN npm ci --only=production && npm cache clean --force

# Copiar arquivos da aplicação
COPY production-server/server.js ./
COPY public/ ./public/

# Ajustar permissões
RUN chown -R nodejs:nodejs /app
USER nodejs

# Variáveis de ambiente
ENV NODE_ENV=production
ENV PORT=3000

# Expor porta
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/api/stats', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Comando para iniciar
CMD ["node", "server.js"]
