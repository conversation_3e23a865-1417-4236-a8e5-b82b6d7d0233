const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 8080;

// Mapeamento de extensões para tipos MIME
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.otf': 'font/otf'
};

// Função para servir arquivos estáticos
function serveStatic(filePath, res) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Arquivo não encontrado');
      return;
    }

    const ext = path.extname(filePath);
    const mimeType = mimeTypes[ext] || 'application/octet-stream';
    
    res.writeHead(200, { 'Content-Type': mimeType });
    res.end(data);
  });
}

// Criar servidor HTTP
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  // Log das requisições
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);

  // Health check endpoint
  if (pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'turfeiras-kepler-frontend'
    }));
    return;
  }

  // Servir arquivos estáticos
  let filePath;
  
  if (pathname.startsWith('/data/')) {
    filePath = path.join(__dirname, pathname);
  } else if (pathname.startsWith('/uploads/')) {
    filePath = path.join(__dirname, pathname);
  } else if (pathname === '/' || pathname.endsWith('/')) {
    filePath = path.join(__dirname, 'dist', 'index.html');
  } else {
    // Tentar servir arquivo da pasta dist
    filePath = path.join(__dirname, 'dist', pathname);
  }

  // Verificar se o arquivo existe
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // Se não encontrar o arquivo, servir index.html para SPA routing
      filePath = path.join(__dirname, 'dist', 'index.html');
    }
    serveStatic(filePath, res);
  });
});

// Iniciar servidor
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Turfeiras Kepler.gl frontend rodando na porta ${PORT}`);
  console.log(`🌍 Acesse em: http://localhost:${PORT}`);
  console.log(`❤️  Health check: http://localhost:${PORT}/health`);
});
