const express = require('express');
const path = require('path');
const compression = require('compression');
const helmet = require('helmet');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 8080;

// Middlewares de segurança e performance
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: [
                "'self'", 
                "'unsafe-inline'", 
                "'unsafe-eval'",
                "https://unpkg.com",
                "https://api.mapbox.com",
                "https://tiles.stadiamaps.com"
            ],
            styleSrc: [
                "'self'", 
                "'unsafe-inline'",
                "https://api.mapbox.com"
            ],
            imgSrc: [
                "'self'", 
                "data:", 
                "blob:",
                "https:",
                "https://api.mapbox.com",
                "https://tiles.stadiamaps.com"
            ],
            connectSrc: [
                "'self'",
                "https://api.mapbox.com",
                "https://tiles.stadiamaps.com",
                "https://events.mapbox.com"
            ],
            fontSrc: [
                "'self'",
                "https://api.mapbox.com"
            ],
            workerSrc: [
                "'self'",
                "blob:"
            ]
        }
    }
}));

app.use(compression());
app.use(cors());
app.use(express.json());

// Servir arquivos estáticos
app.use(express.static(path.join(__dirname, '../public')));

// API endpoints para dados das turfeiras
app.get('/api/turfeiras', (req, res) => {
    // Dados simulados - em produção viriam de um banco de dados
    const turfeirasData = {
        type: "FeatureCollection",
        features: [
            {
                type: "Feature",
                geometry: {
                    type: "Point",
                    coordinates: [-60.0261, -2.5297]
                },
                properties: {
                    id: 1,
                    nome: "Turfeira Amazônica Norte",
                    area_ha: 145.5,
                    carbono_ton: 2890.3,
                    estado: "Amazonas",
                    tipo_vegetacao: "Floresta Ombrófila",
                    data_registro: "2024-01-15",
                    status: "Conservada",
                    ameacas: ["Desmatamento", "Queimadas"],
                    coordenadas_utm: "20M 0834567 9720123"
                }
            },
            {
                type: "Feature",
                geometry: {
                    type: "Point",
                    coordinates: [-56.6289, -19.2563]
                },
                properties: {
                    id: 2,
                    nome: "Turfeira do Pantanal Sul",
                    area_ha: 89.2,
                    carbono_ton: 1567.8,
                    estado: "Mato Grosso do Sul",
                    tipo_vegetacao: "Savana Florestada",
                    data_registro: "2024-02-20",
                    status: "Degradada",
                    ameacas: ["Pecuária", "Agricultura"],
                    coordenadas_utm: "21K 0456789 7876543"
                }
            },
            {
                type: "Feature",
                geometry: {
                    type: "Point",
                    coordinates: [-47.8828, -15.7939]
                },
                properties: {
                    id: 3,
                    nome: "Turfeira Cerrado Central",
                    area_ha: 234.7,
                    carbono_ton: 4123.9,
                    estado: "Distrito Federal",
                    tipo_vegetacao: "Cerrado Stricto Sensu",
                    data_registro: "2024-03-10",
                    status: "Conservada",
                    ameacas: ["Urbanização"],
                    coordenadas_utm: "23L 0198765 8254321"
                }
            },
            {
                type: "Feature",
                geometry: {
                    type: "Point",
                    coordinates: [-43.1729, -22.9068]
                },
                properties: {
                    id: 4,
                    nome: "Turfeira Mata Atlântica",
                    area_ha: 67.3,
                    carbono_ton: 945.6,
                    estado: "Rio de Janeiro",
                    tipo_vegetacao: "Mata Atlântica",
                    data_registro: "2024-04-05",
                    status: "Em Recuperação",
                    ameacas: ["Urbanização", "Poluição"],
                    coordenadas_utm: "23K 0665432 7468901"
                }
            },
            {
                type: "Feature",
                geometry: {
                    type: "Point",
                    coordinates: [-40.5061, -9.3122]
                },
                properties: {
                    id: 5,
                    nome: "Turfeira Caatinga Nordeste",
                    area_ha: 123.8,
                    carbono_ton: 1876.4,
                    estado: "Pernambuco",
                    tipo_vegetacao: "Caatinga Arbórea",
                    data_registro: "2024-05-12",
                    status: "Monitorada",
                    ameacas: ["Seca", "Extração Irregular"],
                    coordenadas_utm: "24L 0789012 8967543"
                }
            }
        ]
    };
    
    res.json(turfeirasData);
});

// API para estatísticas
app.get('/api/stats', (req, res) => {
    const stats = {
        total_turfeiras: 5,
        area_total_ha: 660.5,
        carbono_total_ton: 11403.0,
        estados_mapeados: 5,
        tipos_vegetacao: 5,
        status_conservacao: {
            conservada: 2,
            degradada: 1,
            em_recuperacao: 1,
            monitorada: 1
        },
        principais_ameacas: [
            { nome: "Desmatamento", ocorrencias: 2 },
            { nome: "Urbanização", ocorrencias: 2 },
            { nome: "Queimadas", ocorrencias: 1 },
            { nome: "Pecuária", ocorrencias: 1 },
            { nome: "Agricultura", ocorrencias: 1 }
        ]
    };
    
    res.json(stats);
});

// Rota para análise de carbono
app.get('/api/carbono/analise', (req, res) => {
    const analise = {
        sequestro_anual_ton: 1140.3,
        potencial_emissao_ton: 2850.75,
        beneficio_economico_usd: 85000,
        comparacao_carros_equivalente: 2468,
        projecao_10_anos: {
            cenario_conservacao: 11403 * 1.02,
            cenario_degradacao: 11403 * 0.85,
            diferenca_ton: 11403 * 0.17
        }
    };
    
    res.json(analise);
});

// Fallback para SPA
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Iniciar servidor
app.listen(PORT, () => {
    console.log(`🌿 Servidor Turfeiras Brasil rodando na porta ${PORT}`);
    console.log(`📍 Acesse: http://localhost:${PORT}`);
    console.log(`🔗 API: http://localhost:${PORT}/api/turfeiras`);
    console.log(`📊 Stats: http://localhost:${PORT}/api/stats`);
});

module.exports = app;
